import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, IsUUID, IsBoolean, ValidateNested } from 'class-validator';

export class CreateRoleDto {
  @Type(() => RoleDto)
  @ValidateNested({ each: true })
  info: RoleDto[];
}

export class RoleDto {
  @ApiProperty({
    description: 'Role name',
    example: 'Doctor',
  })
  @IsNotEmpty({ message: 'Role name is required' })
  @IsString({ message: 'Role name must be a string' })
  role_name: string;

  @ApiProperty({
    description: 'Role code (unique short identifier)',
    example: 'DOC001',
  })
  @IsOptional()
  @IsString({ message: 'Role code must be a string' })
  role_code?: string;

  @ApiProperty({
    description: 'Description of the role',
    example: 'Role assigned to doctors',
  })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @ApiProperty({
    description: 'Type of role (default SYSTEM_DEFINED)',
    example: 'SYSTEM_DEFINED',
    default: 'SYSTEM_DEFINED',
  })
  @IsOptional()
  @IsString({ message: 'Type must be a string' })
  type?: string;

  @ApiProperty({
    description: 'Tenant ID if applicable',
    example: '6fd6be04-e7e3-448b-8dc3-dbc6636563a3',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Tenant id must be a valid UUID' })
  tenant_id?: string;

  @ApiProperty({
    description: 'Whether the role is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'isActive must be a boolean' })
  isActive?: boolean;

  @ApiProperty({
    description: 'Created by user',
    example: 'ADMIN',
    default: 'ADMIN',
  })
  @IsOptional()
  @IsString({ message: 'Created by must be a string' })
  created_by?: string;
}
