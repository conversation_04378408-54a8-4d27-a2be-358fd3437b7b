-- CreateTable
CREATE TABLE "doctor_custom_note" (
    "custom_note_id" UUID NOT NULL,
    "doctor_id" UUID NOT NULL,
    "patient_id" UUID NOT NULL,
    "encounter_id" UUID NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "doctor_custom_note_pkey" PRIMARY KEY ("custom_note_id")
);

-- CreateTable
CREATE TABLE "doctor_custom_section" (
    "section_id" UUID NOT NULL,
    "doctor_id" UUID NOT NULL,
    "section_name" TEXT NOT NULL,
    "section_template" TEXT,
    "usage_count" INTEGER NOT NULL DEFAULT 0,
    "last_used" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "doctor_custom_section_pkey" PRIMARY KEY ("section_id")
);

-- CreateTable
CREATE TABLE "doctor_custom_note_section" (
    "note_section_id" UUID NOT NULL,
    "custom_note_id" UUID NOT NULL,
    "custom_section_id" UUID NOT NULL,
    "content" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "doctor_custom_note_section_pkey" PRIMARY KEY ("note_section_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "doctor_custom_section_doctor_id_section_name_key" ON "doctor_custom_section"("doctor_id", "section_name");

-- AddForeignKey
ALTER TABLE "doctor_custom_note" ADD CONSTRAINT "doctor_custom_note_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "doctor"("doctor_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "doctor_custom_note" ADD CONSTRAINT "doctor_custom_note_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "patient"("patient_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "doctor_custom_note" ADD CONSTRAINT "doctor_custom_note_encounter_id_fkey" FOREIGN KEY ("encounter_id") REFERENCES "encounter"("encounter_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "doctor_custom_section" ADD CONSTRAINT "doctor_custom_section_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "doctor"("doctor_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "doctor_custom_note_section" ADD CONSTRAINT "doctor_custom_note_section_custom_note_id_fkey" FOREIGN KEY ("custom_note_id") REFERENCES "doctor_custom_note"("custom_note_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "doctor_custom_note_section" ADD CONSTRAINT "doctor_custom_note_section_custom_section_id_fkey" FOREIGN KEY ("custom_section_id") REFERENCES "doctor_custom_section"("section_id") ON DELETE RESTRICT ON UPDATE CASCADE;
