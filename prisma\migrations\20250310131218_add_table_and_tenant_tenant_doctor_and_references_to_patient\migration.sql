-- AlterTable
ALTER TABLE "patient" ADD COLUMN     "tenant_id" UUID;

-- CreateTable
CREATE TABLE "tenant" (
    "Id" UUID NOT NULL,
    "name" TEXT,
    "phone_number" TEXT,
    "email" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "tenant_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "tenant_doctor" (
    "id" UUID NOT NULL,
    "tenantId" UUID,
    "doctorId" UUID,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "tenant_doctor_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tenant_name_key" ON "tenant"("name");

-- AddForeignKey
ALTER TABLE "patient" ADD CONSTRAINT "patient_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenant"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_doctor" ADD CONSTRAINT "tenant_doctor_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenant"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_doctor" ADD CONSTRAINT "tenant_doctor_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "doctor"("doctor_id") ON DELETE SET NULL ON UPDATE CASCADE;
