const fs = require('fs');
const path = require('path');

/**
 * Permission seed function
 * @param {import('@prisma/client').PrismaClient} prisma
 */

module.exports = async function (prisma) {
    const filePath = path.join(__dirname, 'response_types.json');
    const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    const responseTypes = data.responseTypes;
    const result = await prisma.ResponseType.createMany({
        data: responseTypes.map((r) => ({
            type_name: r.type_name,
            label: r.label,
            created_by: 'ADMIN'
        })),
        skipDuplicates: true
    });
    console.log(`Seeded ${result.count} Response Types`)
}