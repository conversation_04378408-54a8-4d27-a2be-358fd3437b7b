const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function runSeeds() {
  console.log("🌱 Starting database seed...");

  // Run seeds in sequence
  await require('./permissions.seed')(prisma);
  await require('./roles.seed')(prisma);
  await require('./response_types.seed')(prisma);
  await require('./roles_permission.seed')(prisma);
  await require('./org-setting.seed')(prisma);
  console.log("✅ All seeds completed.");
}

runSeeds()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
