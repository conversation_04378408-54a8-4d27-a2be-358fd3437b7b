-- This is an auto code generator migration.
CREATE OR REPLACE FUNCTION  ekkomd_core.generate_auto_code(
	prefixval text,
	suffixval text,
	created_byval text,
	updated_byval text)
    RETURNS text
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
AS $BODY$
DECLARE lstincr integer;
DECLARE	code text;
BEGIN
	IF prefixval!='' AND suffixval=''  THEN
	BEGIN
		IF EXISTS(SELECT  FROM  "ekkomd_core".tbl_auto_code AC WHERE AC.prefix=prefixval AND "isActive"=true) THEN
		 lstincr:=(SELECT last_increment from  "ekkomd_core".tbl_auto_code AC WHERE AC.prefix=prefixval AND "isActive"=true);
		 lstincr:=lstincr+1;
		 code:=prefixval || '_' || CAST(lstincr as Text);
		 UPDATE  "ekkomd_core".tbl_auto_code SET last_generated_code=code,last_increment=lstincr,updated_by=UPDATED_BYVAL WHERE prefix=prefixval AND "isActive"=true;
		 RETURN CODE;
		ELSE
		CODE:=prefixval  || '_1';
		INSERT INTO  "ekkomd_core".tbl_auto_code (prefix,last_generated_code,last_increment,created_by,updated_by) VALUES(prefixval,CODE,1,CREATED_BYVAL,UPDATED_BYVAL);
		RETURN CODE;
		END IF;
	 EXCEPTION WHEN OTHERS THEN
	 ROLLBACK;
	 END;
	ELSIF suffixval!='' AND prefixval='' THEN
	BEGIN
 		IF EXISTS(SELECT FROM  "ekkomd_core".tbl_auto_code AC WHERE AC.suffix=suffixval AND "isActive"=true) THEN
		 lstincr:=(SELECT last_increment from  "ekkomd_core".tbl_auto_code AC WHERE AC.suffix=suffixval AND "isActive"=true);
		 lstincr:=lstincr+1;
		 code:= CAST(lstincr as Text)|| '_' || suffixval;
		 UPDATE  "ekkomd_core".tbl_auto_code SET last_generated_code=code,last_increment=lstincr,updated_by=UPDATED_BYVAL WHERE suffix=suffixval AND "isActive"=true;
		 RETURN CODE;
		ELSE
		CODE:= '1_' || suffixval;
		INSERT INTO  "ekkomd_core".tbl_auto_code (suffix,last_generated_code,last_increment,created_by,updated_by) VALUES(suffixval,CODE,1,CREATED_BYVAL,UPDATED_BYVAL);
		RETURN CODE;
		END IF;
		EXCEPTION WHEN OTHERS THEN
		ROLLBACK;
		END;
	ELSE
	CODE:=NULL;
	END IF;
	
END;
$BODY$;