import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Put,
  Query,
  Post,
  Delete,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { PatientService } from './patient.service';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { PatientDetailsDTO } from './dtos/PatientDetails.dto';
import { Patient } from '@prisma/client';
import {
  ApiResponseDTO,
  PaginatedResultDTO,
  PaginationQueryDTO,
} from 'src/common/dtos';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { CreatePatientDetailsDto } from './dtos/CreatePatientDetails.dto';
import { UtilsService } from 'src/common/utils/utils.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { PERMISSIONS } from 'src/common/decorators/permissions.decorator';

@ApiTags('Patients')
@ApiBearerAuth('access-token')
@Controller('patients')
export class PatientController {
  constructor(
    private readonly patientservice: PatientService,
    private readonly utilsService: UtilsService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get patient details by phone number' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'phone',
    required: false,
    type: Number,
    description: 'phone number for patient',
  })
  @ApiQuery({
    name: 'full_name',
    required: false,
    type: String,
    description: 'patient full name',
  })
  @ApiQuery({
    name: 'phone_or_name',
    required: false,
    type: String,
    description: 'patient phone or name',
  })
  @PERMISSIONS('patient.read.self', 'patient.read.org')
  async getPatientByPhone(
    @Query() pagination: PaginationQueryDTO,
    @Query('phone') phone: string,
    @Query('full_name') full_name: string,
    @Query('phone_or_name') phone_or_name: string,
    @GetUserEmail() userEmail: string,
  ): Promise<PaginatedResultDTO<Patient>> {
    const { page, limit } = pagination;

    if (phone_or_name && phone_or_name != '') {
      const { characters, numbers } =
        this.utilsService.separateCharactersAndNumbers(phone_or_name);
      phone = numbers;
      full_name = characters != '' ? characters : undefined;
    }

    phone = !phone || phone == '' ? undefined : phone;

    return await this.patientservice.getPatientByPhone(
      phone,
      userEmail,
      page,
      limit,
      full_name,
      phone_or_name,
    );
  }

  @Put('/:patientId')
  @ApiOperation({ summary: 'Update patient details' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('patientImage'))
  @PERMISSIONS('patient.update.self', 'patient.update.org')
  async updatePatientDetails(
    @Body() model: CreatePatientDetailsDto,
    @UploadedFile() patientImage: Express.Multer.File, // 👈 uploaded image
    @GetUserEmail() userEmail: string,
    @Param('patientId', ParseUUIDPipe) patientId: string,
  ) {
    return await this.patientservice.updatePatientDetails(
      model,
      userEmail,
      patientId,
      patientImage,
    );
  }

  @Post()
  @ApiOperation({ summary: 'Create patient details' })
  @PERMISSIONS('patient.create.self', 'patient.create.org')
  async createPatient(
    @Body() model: CreatePatientDetailsDto,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.patientservice.insertPatient(model, userEmail);
  }

  @Delete('/:patientId')
  @ApiOperation({ summary: 'Delete patient details' })
  @PERMISSIONS('patient.delete.self', 'patient.delete.org')
  async deletePatient(
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.patientservice.deletePatient(patientId, userEmail);
  }

  @Get()
  @ApiOperation({ summary: 'Get all patients' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @PERMISSIONS('patient.read.self', 'patient.read.org')
  async getAllPatient(
    @Query() pagination: PaginationQueryDTO,
    @GetUserEmail() userEmail: string,
  ): Promise<PaginatedResultDTO<Patient>> {
    const { page, limit } = pagination;
    return await this.patientservice.getAllPatient(page, limit, userEmail);
  }

  @Get('/:patientId')
  @PERMISSIONS('patient.read.self', 'patient.read.org')
  async getPatientById(
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.patientservice.getPatientById(patientId, userEmail);
  }

  @Get('vitals-2y/:patientId')
  @PERMISSIONS('vitals.read.self', 'vitals.read.org')
  async getVitalsByPatientId(
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.patientservice.getVitalsByPatientId(patientId, userEmail);
  }

  @Get('encounters/:patientId')
  @ApiOperation({ summary: 'Get encounters by patient ID' })
  @PERMISSIONS('visit.read.self', 'visit.read.org')
  async getEncountersByPatientId(
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.patientservice.getEncountersByPatientId(
      patientId,
      userEmail,
    );
  }
}
