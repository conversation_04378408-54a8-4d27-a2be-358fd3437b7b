-- CreateTable
CREATE TABLE "doctor_letterhead_section" (
    "id" UUID NOT NULL,
    "letterhead_id" UUID NOT NULL,
    "letterhead_section_id" UUID NOT NULL,
    "value" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "doctor_letterhead_section_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "doctor_letterhead_section" ADD CONSTRAINT "doctor_letterhead_section_letterhead_id_fkey" FOREIGN KEY ("letterhead_id") REFERENCES "doctor_letterhead"("letterhead_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "doctor_letterhead_section" ADD CONSTRAINT "doctor_letterhead_section_letterhead_section_id_fkey" FOREIGN KEY ("letterhead_section_id") REFERENCES "letterhead_section"("letterhead_section_id") ON DELETE RESTRICT ON UPDATE CASCADE;
