-- This is a trigger for generating auto record id  migration.
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION generate_auto_record_id_trigger()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
DECLARE
    prefix TEXT;
    generated_record_id TEXT;
BEGIN
    -- Get the values of the columns you want to use for generating the code
    prefix := NEW.master_code_prefix;
    

    -- Generate the auto code using your function
    generated_record_id := generate_auto_code(prefix, '', '', '');

    -- Update the auto_code column with the generated value
    NEW.record_id := generated_record_id;

    RETURN NEW;
END;
$BODY$;


CREATE OR REPLACE TRIGGER generate_auto_record_id_trigger
    BEFORE INSERT
    ON tbl_universal_picklist_data
    FOR EACH ROW
    EXECUTE FUNCTION generate_auto_record_id_trigger();