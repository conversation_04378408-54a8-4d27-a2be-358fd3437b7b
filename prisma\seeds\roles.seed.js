/**
 * Role seed function
 * @param {import('@prisma/client').PrismaClient} prisma
 */
module.exports = async function (prisma) {
    const roles = [
        { role_name: 'Doctor', role_code: 'doctor' },
        { role_name: 'Staff', role_code: 'staff' },
        { role_name: 'Org Admin', role_code: 'org_admin' },
        { role_name: 'Super Admin', role_code: 'super_admin' },
    ];

    let inserted = 0;
    let updated = 0;

    for (const role of roles) {
        const existing = await prisma.tbl_role_lk.findFirst({
            where: { role_code: role.role_code },
        });

        if (existing) {
            await prisma.tbl_role_lk.update({
                where: { role_id: existing.role_id }, // primary key
                data: {
                    role_name: role.role_name,
                    updated_by: 'ADMIN',
                },
            });
            updated++;
        } else {
            await prisma.tbl_role_lk.create({
                data: {
                    role_name: role.role_name,
                    role_code: role.role_code,
                    created_by: 'ADMI<PERSON>',
                },
            });
            inserted++;
        }
    }

    console.log(`✅ Seeded roles. Inserted: ${inserted}, Updated: ${updated}`);
};
