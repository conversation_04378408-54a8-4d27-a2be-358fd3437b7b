import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUI<PERSON>ip<PERSON>,
  Post,
  Put,
  Delete,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserPermissionService } from './user-permission.service';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { tbl_user_permission_mapping } from '@prisma/client';
import { CreateUserPermissionMappingDto } from './dtos/create-user-permission-mapping.dto';
import { PERMISSIONS } from 'src/common/decorators/permissions.decorator';

@ApiTags('UserPermissionMapping')
@ApiBearerAuth('access-token')
@Controller('user-permission')
export class UserPermissionController {
  constructor(private readonly userPermService: UserPermissionService) {}

  @Post()
  @ApiOperation({ summary: 'Create a user-permission mapping' })
  @PERMISSIONS('assign.permissions.basic', 'assign.permissions.all')
  async create(
    @Body() dto: CreateUserPermissionMappingDto,
    @GetUserEmail() userEmail: string,
  ): Promise<tbl_user_permission_mapping> {
    return await this.userPermService.create(dto, userEmail);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user-permission mapping by ID' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<tbl_user_permission_mapping> {
    return await this.userPermService.findOne(id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all user-permission mappings' })
  async findAll(): Promise<tbl_user_permission_mapping[]> {
    return await this.userPermService.findAll();
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a user-permission mapping' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() dto: Partial<CreateUserPermissionMappingDto>,
    @GetUserEmail() userEmail: string,
  ): Promise<tbl_user_permission_mapping> {
    return await this.userPermService.update(id, dto, userEmail);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete (soft delete) user-permission mapping' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUserEmail() userEmail: string,
  ): Promise<tbl_user_permission_mapping> {
    return await this.userPermService.remove(id, userEmail);
  }
}
