import { Injectable } from '@nestjs/common';
import { PatientRepository } from './patient.repository';
import { PatientDetailsDTO } from './dtos/PatientDetails.dto';
import { Patient } from '@prisma/client';
import { CreatePatientDetailsDto } from './dtos/CreatePatientDetails.dto';
import { PaginatedResultDTO } from 'src/common/dtos';

@Injectable()
export class PatientService {
  constructor(private readonly patientRepository: PatientRepository) { }

  async getPatientByPhone(
    phone: string,
    userEmail: string,
    page: number = 0,
    limit: number = 10,
    full_name: string | undefined = undefined,
    phone_or_name: string | undefined = undefined,
  ): Promise<PaginatedResultDTO<Patient>> {
    return await this.patientRepository.getPatientByPhone(
      phone,
      userEmail,
      page,
      limit,
      full_name,
      phone_or_name,
    );
  }

  async updatePatientDetails(
    model: CreatePatientDetailsDto,
    userEmail: string,
    patientId: string,
    patientImage?: Express.Multer.File,
  ): Promise<Patient> {
    return await this.patientRepository.updatePatient(
      model,
      userEmail,
      patientId,
      patientImage,
    );
  }


  async insertPatient(
    data: CreatePatientDetailsDto,
    userEmail: string,
  ): Promise<Patient> {
    return await this.patientRepository.insertPatient(data, userEmail);
  }

  async deletePatient(patient_id: string, userEmail: string): Promise<Patient> {
    return await this.patientRepository.deletePatient(patient_id, userEmail);
  }

  async getAllPatient(
    page: number,
    limit: number,
    userEmail: string,
  ): Promise<PaginatedResultDTO<Patient>> {
    return await this.patientRepository.getAllPatient(page, limit, userEmail);
  }

  async getPatientById(
    patient_id: string,
    userEmail: string,
  ): Promise<Patient> {
    return await this.patientRepository.getPatientById(patient_id, userEmail);
  }

  async getVitalsByPatientId(
    patient_id: string,
    userEmail: string,
  ): Promise<Patient> {
    return await this.patientRepository.getVitalsByPatientId(patient_id, userEmail);
  }

  async getEncountersByPatientId(patient_id: string, userEmail: string): Promise<any[]> {
  return await this.patientRepository.getEncountersByPatientId(patient_id, userEmail);
}


}
