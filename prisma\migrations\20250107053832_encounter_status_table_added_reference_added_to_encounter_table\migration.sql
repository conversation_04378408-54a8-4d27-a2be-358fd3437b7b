-- AlterTable
ALTER TABLE "encounter" ADD COLUMN     "encounter_status" VARCHAR(50);

-- CreateTable
CREATE TABLE "encounter_status" (
    "status" TEXT NOT NULL,
    "label" VARCHAR(50) NOT NULL,
    "description" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "encounter_status_pkey" PRIMARY KEY ("status")
);

-- AddForeignKey
ALTER TABLE "encounter" ADD CONSTRAINT "encounter_encounter_status_fkey" FOREIGN KEY ("encounter_status") REFERENCES "encounter_status"("status") ON DELETE SET NULL ON UPDATE CASCADE;
