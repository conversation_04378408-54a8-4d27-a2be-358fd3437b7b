/*
  Warnings:

  - Made the column `phone_country_code` on table `patient` required. This step will fail if there are existing NULL values in that column.
  - Made the column `phone_number` on table `patient` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "doctor" ALTER COLUMN "updated_at" DROP NOT NULL,
ALTER COLUMN "updated_by" DROP NOT NULL,
ALTER COLUMN "updated_by" DROP DEFAULT;

-- AlterTable
ALTER TABLE "encounter" ALTER COLUMN "updated_at" DROP NOT NULL,
ALTER COLUMN "updated_by" DROP NOT NULL,
ALTER COLUMN "updated_by" DROP DEFAULT;

-- AlterTable
ALTER TABLE "patient" ALTER COLUMN "updated_at" DROP NOT NULL,
ALTER COLUMN "updated_by" DROP NOT NULL,
ALTER COLUMN "updated_by" DROP DEFAULT,
ALTER COLUMN "phone_country_code" SET NOT NULL,
ALTER COLUMN "phone_number" SET NOT NULL;
