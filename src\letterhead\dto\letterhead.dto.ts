import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsUUID, ValidateNested } from 'class-validator';

export class UpdateLetterHeadDto {
  page_size: string;
  page_height: number;
  page_width: number;
  top_padding: number;
  top_padding_px: number;
  bottom_padding: number;
  bottom_padding_px: number;
  units: string;
}

export class UpsertDoctorLetterheadSectionDto {
  @ApiProperty({
    description: 'letterhead_id',
    example: '6fd6be04-e7e3-448b-8dc3-dbc6636563a3',
  })
  @IsNotEmpty()
  @IsUUID()
  letterhead_id: string;
  @ApiProperty({
    description: 'letterhead_section_id',
    example: '6fd6be04-e7e3-448b-8dc3-dbc6636563a3',
  })
  @IsNotEmpty()
  @IsUUID()
  letterhead_section_id: string;

  @ApiProperty({
    description: 'value',
    example: 'this value of custom letterhead  section',
  })
  @IsNotEmpty()
  value: string;
}

export class upsertMultipleDoctorLetterheadSectionsDto {
  @Type(() => UpsertDoctorLetterheadSectionDto)
  @ValidateNested({ each: true })
  info: UpsertDoctorLetterheadSectionDto[];
}
