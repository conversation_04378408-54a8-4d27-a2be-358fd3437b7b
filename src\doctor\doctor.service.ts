import { Injectable } from '@nestjs/common';
import { UpsertDoctorDto } from './dtos/DoctorDetails.dto';
import { DoctorRepository } from './doctor.repository';

@Injectable()
export class DoctorService {
  constructor(private readonly doctorrepository: DoctorRepository) {}
  async upsertDoctor(model: UpsertDoctorDto, userEmail: string) {
    return await this.doctorrepository.upsertDoctor(model, userEmail);
  }

  async getAllSpecialities() {
    return await this.doctorrepository.getAllSpecialities();
  }

  async getDoctorDetailsByEmail(userEmail: string) {
    return await this.doctorrepository.getDoctorByEmail(userEmail);
  }
}
