import { Body, Controller, Get, Post } from '@nestjs/common';
import { TenantService } from './tenant.service';
import { CreateTenantDto } from './dto/tenant.dto';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';

@Controller('tenants')
export class TenantController {
  constructor(private readonly tenantService: TenantService) {}

  @Get()
  async getAllTenants() {
    return await this.tenantService.getAllTenants();
  }

  @Post()
  async createTenant(
    @Body() model: CreateTenantDto,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.tenantService.createTenant(model, userEmail);
  }

  @Get('doctor-tenant')
  async getDoctorTenants(@GetUserEmail() userEmail: string) {
    return await this.tenantService.getDoctorTenants(userEmail);
  }
}
