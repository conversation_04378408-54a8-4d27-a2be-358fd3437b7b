import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { LetterheadService } from './letterhead.service';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import {
  UpdateLetterHeadDto,
  UpsertDoctorLetterheadSectionDto,
  upsertMultipleDoctorLetterheadSectionsDto,
} from './dto/letterhead.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('letterhead')
export class LetterheadController {
  constructor(private readonly letterheadService: LetterheadService) {}

  @Get('default')
  async getDefaultLetterHead(@GetUserEmail() userEmail: string) {
    return await this.letterheadService.getOrCreateLetterHead(userEmail);
  }

  @Patch(':letterhead_id')
  async updateLetterHead(
    @GetUserEmail() userEamil: string,
    @Body() model: UpdateLetterHeadDto,
    @Param('letterhead_id', ParseUUIDPipe) letterhead_id: string,
  ) {
    return await this.letterheadService.updateLetterHead(
      userEamil,
      model,
      letterhead_id,
    );
  }

  @Get('page-sizes')
  async getAvailablePageSizes(@GetUserEmail() userEmail: string) {
    return await this.letterheadService.getPagesizes(userEmail);
  }

  @Get('doctor-letterhead-section-values')
  async getDoctorLetterheadSectionWithValues(
    @GetUserEmail() userEmail: string,
  ) {
    return await this.letterheadService.getDoctorLetterheadSectionWithValues(
      userEmail,
    );
  }

  @Get('letterhead-sections')
  async getAllLetterheadSections() {
    return await this.letterheadService.getAllLetterheadSections();
  }

  @Post('upsert-doctor-letterhead-section')
  async upsertDoctorLetterheadsections(
    @GetUserEmail() userEmail: string,
    @Body() model: upsertMultipleDoctorLetterheadSectionsDto,
  ) {
    return await this.letterheadService.upsertMultipleDoctorLetterheadsections(
      userEmail,
      model,
    );
  }

  @Patch('toggle/default')
  async toggleDefaultLetterHead(@GetUserEmail() userEmail: string) {
    return await this.letterheadService.toggleLetterhead(userEmail);
  }

  @Post('image-upload/:letterheadId/:letterheadSectionId')
  @UseInterceptors(
    FileInterceptor('image', {
      fileFilter: (req, file, cb) => {
        const allowedTypes = [
          'image/jpeg',
          'image/jpg',
          'image/png',
          'image/gif',
          'image/bmp',
          'image/webp',
          'image/tiff',
          'image/svg+xml',
          'image/x-icon',
        ];
        if (allowedTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error('Only images are supported '), false);
        }
        if (file.size > 1024 * 1024 * 10) {
          cb(new Error('File size should be less than 10MB'), false);
        }
      },
    }),
  )
  async upsertImageDoctorLetterheadSection(
    @UploadedFile() file: Express.Multer.File,
    @GetUserEmail() userEmail: string,
    @Param('letterheadId', ParseUUIDPipe) letterhead_id: string,
    @Param('letterheadSectionId', ParseUUIDPipe) letterhead_section_id: string,
  ) {
    return await this.letterheadService.upsertImageDoctorLetterheadSection(
      userEmail,
      letterhead_id,
      letterhead_section_id,
      file,
    );
  }

  @Get('image/:letterheadId/:letterheadSectionId')
  async downloadLetterheadLogo(
    @GetUserEmail() userEmail: string,
    @Param('letterheadId', ParseUUIDPipe) lettehead_id: string,
    @Param('letterheadSectionId', ParseUUIDPipe) letterhead_section_id: string,
  ) {
    return await this.letterheadService.downloadLetterheadlogo(
      lettehead_id,
      letterhead_section_id,
    );
  }
}
