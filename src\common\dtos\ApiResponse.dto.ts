import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationResDTO } from './PaginationRes.dto';

export class ApiResponseDTO<T> {
  @ApiProperty({
    description: 'HTTP status code',
    example: 201,
  })
  statusCode?: number;

  @ApiProperty({
    description: 'Request path',
    example: '/encounter',
  })
  path?: string;

  @ApiProperty({
    description: 'Response message',
    example: 'Encounter created successfully',
  })
  message?: string;

  @ApiProperty({
    description: 'Response data',
    type: Object,
  })
  data: T;

  @ApiPropertyOptional({
    description: 'Pagination metadata',
    type: PaginationResDTO,
  })
  pagination?: PaginationResDTO;

  @ApiPropertyOptional({
    description: 'current api version',
  })
  apiVersion?: string;

  @ApiPropertyOptional({
    description: 'current timestamp',
  })
  timestamp?: string;

  @ApiPropertyOptional({
    description: 'error',
  })
  error?: any;
}
