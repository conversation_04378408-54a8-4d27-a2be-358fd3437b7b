FROM node:20


ARG DATABASE_URL

# Install FFmpeg
RUN apt-get update && \
    apt-get install -y ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY package*.json ./
COPY prisma ./prisma/


ENV DATABASE_URL=$DATABASE_URL \
    FFMPEG_PATH=/usr/bin/ffmpeg \
    FFPROBE_PATH=/usr/bin/ffprobe 


RUN npm install

## for deployment
RUN npx prisma migrate deploy 

RUN npx prisma generate

RUN npx prisma db seed

COPY . .

RUN npm run build

EXPOSE 3002

CMD ["npm","run","start:prod"]