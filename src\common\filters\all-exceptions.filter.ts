import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiResponseDTO } from '../dtos';
import { apiVersion } from '../constants/common.constants';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;
    const message = this.PrismaErrorHandler(exception);

    // Log the error details
    this.logger.error(
      `HTTP Status: ${status} Error Message: ${JSON.stringify(message)} Path: ${request.url}`,
    );

    this.logger.error(
      `Error Stack: ${exception?.stack} \nPath: ${request.url}`,
    );

    const res: ApiResponseDTO<null> = {
      statusCode: status,
      message:
        typeof message === 'string'
          ? message
          : (message as any).message || 'Unexpected error',
      path: request.url,
      data: null,
      timestamp: new Date().toISOString(),
      apiVersion: apiVersion,
      error: exception?.stack || null,
    };
    // Format the error response
    response.status(status).json(res);
  }

  PrismaErrorHandler(exception) {
    switch (exception.code) {
      case 'P2002':
        return 'Unique constraint violation';
      case 'P2003':
        return 'Foreign key constraint violation';

      case 'P2004':
        return 'Check constraint violation';

      case 'P2005':
        return 'field value type validation error';

      case '22P02':
        return 'json structure or key name validation error';

      case 'P2010':
        return 'SQL syntx error in the sql statement';

      case 'P2024':
        return 'Connection limit exceeded on db connection pool error';

      default:
        return exception instanceof HttpException
          ? exception.getResponse()
          : exception
            ? exception
            : 'Internal server error';
    }
  }
}
