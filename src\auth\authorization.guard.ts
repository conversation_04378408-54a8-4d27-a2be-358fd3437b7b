import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AccessControlRepository } from 'src/common/access-control/access-control.repository';
import { DECORATOR_KEYS } from 'src/common/constants/common.constants';

export class AuthorizationGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly accesscontrolRepository: AccessControlRepository,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    //TODO MAKE IT DYNAMIC LATER, FOR NOW RETURNING TRUE
    return true;

    const isPublic = this.reflector.getAllAndOverride<boolean>(
      DECORATOR_KEYS.IS_PUBLIC,
      [context.getHandler(), context.getClass()],
    );
    if (isPublic) {
      return true; // Allow access without authentication
    }

    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      DECORATOR_KEYS.PERMISSIONS,
      [context.getHandler(), context.getClass()],
    );

    console.log('requiredPermissions', requiredPermissions);

    if (!requiredPermissions) {
      return true; // No permissions required, allow access
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    const userPermissions =
      await this.accesscontrolRepository.getUserPermissions(user.email);

    let fetchedPermissions: Set<string> = new Set();
    let userRoles = [];

    userPermissions.map((item) => {
      item.tbl_user_permission_mapping.map((item1) => {
        fetchedPermissions.add(item1.tbl_permission_lk.key);
      });
      item.tbl_user_role_mapping.map((item2) => {
        userRoles.push(item2.roleLookup.role_code);
        item2.roleLookup.tbl_role_permission_mapping.map((item3) => {
          fetchedPermissions.add(item3.tbl_permission_lk.key);
        });
      });
    });

    const grantedPermissions: string[] = [...fetchedPermissions];

    request.user.roles = userRoles;
    request.user.permissions = grantedPermissions;

    function GetBasePermissions(arr: string[]): string[] {
      return arr.map((item) => {
        // return item.split('.').slice(0, 2).join('.');
        return item.substring(0,item.lastIndexOf('.'))
      });
    }

    console.log('userRoles', userRoles);
    console.log('grantedPermissions', GetBasePermissions(grantedPermissions));

    if (userRoles.includes('super_admin')) {
      return true;
    }

    return GetBasePermissions(requiredPermissions).every((permission) =>
      GetBasePermissions(grantedPermissions).includes(permission),
    );
  }
}
