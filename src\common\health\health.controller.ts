import { Controller, Get } from '@nestjs/common';
import {
  HealthCheckService,
  HealthCheck,
  HttpHealthIndicator,
} from '@nestjs/terminus';
import { PrismaHealthIndicator } from './prisma-health.indicator';
import { Public } from '../decorators/public-endpoint.decorator';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
    private prismaHealth: PrismaHealthIndicator,
  ) {}

  TIMEOUT = 30000;

  @Public()
  @Get()
  @HealthCheck()
  async check() {
    const healthChecks = [
      () =>
        this.withTimeout(
          () =>
            this.http.pingCheck(
              'auth0',
              'https://dev-6xb2a3j4kezlh7lk.us.auth0.com/',
            ),
          this.TIMEOUT,
        ),
      () =>
        this.withTimeout(
          () => this.prismaHealth.isHealthy('database'),
          this.TIMEOUT,
        ),
    ];

    const result = await this.health.check(healthChecks);

    return { data: result };
  }

  /**
   * Wraps a health check function with a timeout.
   * If the function does not resolve within the specified time, it rejects with a timeout error.
   *
   * @param fn - The health check function to execute.
   * @param ms - The timeout duration in milliseconds.
   * @returns A promise that resolves or rejects based on the health check outcome.
   */
  private withTimeout<T>(fn: () => Promise<T>, ms: number): Promise<T> {
    return Promise.race([
      fn(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Timeout')), ms),
      ),
    ]);
  }
}
