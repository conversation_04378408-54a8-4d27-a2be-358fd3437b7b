import { Module } from '@nestjs/common';
import { SubscriptionController } from './subscription.controller';
import { SubscriptionService } from './subscription.service';
import { SubscriptionRepository } from './subscription.repository';

@Module({
    imports: [],
    controllers: [SubscriptionController],
    providers: [SubscriptionService,SubscriptionRepository],
    exports: [SubscriptionRepository],
})
export class SubscriptionModule {}
