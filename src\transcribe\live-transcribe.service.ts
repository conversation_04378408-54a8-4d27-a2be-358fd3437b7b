import { Injectable, Logger } from '@nestjs/common';
import { TranscribeService } from './transcribe.service';
import { TranscribeRepository } from './transcribe.repository';
import { EventEmitter } from 'events';
import {
  TranscribeStreamingClient,
  StartStreamTranscriptionCommand,
} from '@aws-sdk/client-transcribe-streaming';

interface RealtimeTranscriptionSession {
  encounterId: string;
  eventEmitter: EventEmitter;
  chunks: string[];
  startTime: Date;
  isActive: boolean;
  audioBuffer: Buffer;
  lastTranscript: string;
}

@Injectable()
export class LiveTranscribeService {
  private readonly logger = new Logger(LiveTranscribeService.name);

  // Store transcription sessions
  private realtimeTranscriptionSessions: Map<
    string,
    RealtimeTranscriptionSession
  > = new Map();

  private transcribeClient: TranscribeStreamingClient;

  constructor(
    private readonly transcribeService: TranscribeService,
    private readonly transcribeRepository: TranscribeRepository,
  ) {
    this.transcribeClient = new TranscribeStreamingClient({
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
      region: process.env.AWS_REGION || 'us-east-1',
    });
  }

  /**
   * Start a new real-time transcription session with AWS Transcribe Streaming
   * @param encounterId The encounter ID
   * @returns A session ID for the real-time transcription
   */
  async startRealtimeTranscription(encounterId: string): Promise<string> {
    this.logger.log(
      `Starting real-time transcription for encounter: ${encounterId}`,
    );

    const eventEmitter = new EventEmitter();

    try {
      // Create a new transcription session
      this.realtimeTranscriptionSessions.set(encounterId, {
        encounterId,
        eventEmitter,
        chunks: [],
        startTime: new Date(),
        isActive: true,
        audioBuffer: Buffer.from(''),
        lastTranscript: '',
      });

      // Start the AWS transcription process asynchronously
      this.startAwsTranscription(encounterId).catch((error) => {
        this.logger.error(`AWS Transcription error: ${error.message}`);
        eventEmitter.emit('error', error.message);
      });

      return encounterId;
    } catch (error) {
      this.logger.error(
        `Failed to start real-time transcription: ${error.message}`,
      );
      throw new Error(
        `Failed to start real-time transcription: ${error.message}`,
      );
    }
  }

  /**
   * Start the AWS Transcribe Streaming process
   * @param encounterId The encounter ID
   */
  private async startAwsTranscription(encounterId: string): Promise<void> {
    const session = this.realtimeTranscriptionSessions.get(encounterId);

    if (!session || !session.isActive) {
      return;
    }

    try {
      // Define the audio stream generator function
      const audioStream = async function* (session) {
        while (session.isActive) {
          await new Promise((resolve) => setTimeout(resolve, 0)); // Small delay to prevent CPU hogging

          if (session.audioBuffer.length >= 1024) {
            const chunk = session.audioBuffer.slice(0, 1024);
            session.audioBuffer = session.audioBuffer.slice(1024);
            yield { AudioEvent: { AudioChunk: chunk } };
          }
        }
      };

      // Create the AWS Transcribe command
      const command = new StartStreamTranscriptionCommand({
        LanguageCode: 'en-US', // Can be parameterized based on requirements
        MediaSampleRateHertz: 44100,
        MediaEncoding: 'pcm',
        AudioStream: audioStream(session),
        // AWS SDK expects a single language, not an array
        // LanguageOptions: ["en-US", "ta-IN"], // English and Tamil as per your prompt requirements
      });

      // Send the command to AWS Transcribe
      const response = await this.transcribeClient.send(command);

      // Process the response stream
      for await (const event of response.TranscriptResultStream) {
        if (!session.isActive) break;

        if (event.TranscriptEvent) {
          const results = event.TranscriptEvent.Transcript.Results;
          if (results.length > 0 && results[0].Alternatives.length > 0) {
            const transcript = results[0].Alternatives[0].Transcript;
            const isFinal = !results[0].IsPartial;

            if (isFinal) {
              session.chunks.push(transcript);
              const newPart = transcript.substring(
                session.lastTranscript.length,
              );
              // this.logger.debug(`Final transcription: ${newPart}`);
             
              session.eventEmitter.emit('transcription', newPart);
            } else {
              let newPart = transcript.substring(
                session.lastTranscript.length,
              );
              newPart = session.lastTranscript.trim() === transcript.trim() ? '' : newPart === '' ? transcript : newPart;
              session.lastTranscript = transcript;
              if (newPart.trim() !== '') {
                // this.logger.log(`Partial transcription: ${newPart}`);
                session.eventEmitter.emit('transcription', newPart);
              }
            }
          }
        }
      }
    } catch (error) {
      this.logger.error(`AWS Transcription error: ${error.message}`);
      session.eventEmitter.emit('error', error.message);
    } finally {
      if (session.isActive) {
        session.isActive = false;
        this.logger.log(
          `AWS Transcription ended for encounter: ${encounterId}`,
        );
      }
    }
  }

  /**
   * Send an audio chunk to the real-time transcription session
   * @param encounterId The encounter ID
   * @param audioChunk The audio buffer to process
   * @returns A promise that resolves when the chunk is processed
   */
  async sendAudioChunk(encounterId: string, audioChunk: Buffer): Promise<void> {
    const session = this.realtimeTranscriptionSessions.get(encounterId);

    if (!session || !session.isActive) {
      throw new Error(
        `No active real-time transcription session found with ID: ${encounterId}`,
      );
    }

    try {
      // Add the audio chunk to the buffer
      session.audioBuffer = Buffer.concat([session.audioBuffer, audioChunk]);
      // this.logger.debug(
      //   `Added audio chunk to buffer for encounter: ${encounterId}, buffer size: ${session?.audioBuffer?.length}`,
      // );
    } catch (error) {
      this.logger.error(`Error sending audio chunk: ${error.message}`);
      throw new Error(`Failed to send audio chunk: ${error.message}`);
    }
  }

  /**
   * Subscribe to real-time transcription events
   * @param encounterId The encounter ID
   * @param eventType The event type ('transcription', 'error', or 'close')
   * @param callback The callback function
   */
  onRealtimeTranscription(
    encounterId: string,
    eventType: 'transcription' | 'error' | 'close',
    callback: (data: any) => void,
  ): void {
    const session = this.realtimeTranscriptionSessions.get(encounterId);

    if (!session) {
      throw new Error(
        `No real-time transcription session found with ID: ${encounterId}`,
      );
    }

    session.eventEmitter.on(eventType, callback);
  }

  /**
   * End a real-time transcription session
   * @param encounterId The encounter ID
   * @returns The combined transcription from all chunks
   */
  endRealtimeTranscription(encounterId: string): string {
    const session = this.realtimeTranscriptionSessions.get(encounterId);

    if (!session) {
      throw new Error(
        `No real-time transcription session found with ID: ${encounterId}`,
      );
    }

    try {
      // Mark session as inactive
      session.isActive = false;

      // Get the combined transcription
      const combinedTranscription = session.chunks.join(' ');

      // Clean up resources
      session.eventEmitter.removeAllListeners();
      this.realtimeTranscriptionSessions.delete(encounterId);

      this.logger.log(`Ended real-time transcription session: ${encounterId}`);

      return combinedTranscription;
    } catch (error) {
      this.logger.error(
        `Error ending real-time transcription: ${error.message}`,
      );

      // Clean up even if there's an error
      this.realtimeTranscriptionSessions.delete(encounterId);

      throw new Error(
        `Failed to end real-time transcription: ${error.message}`,
      );
    }
  }

  async processFinalTranscription(
    original_filename: string,
    encounterId: string,
    raw_transcription: string,
    user_email: string,
    s3_uri: string,
    file_size: number,
    mime_type: string = 'audio/wav',
    duration: string = '0',
    transcription_type: string,
    patient_id: string,
  ): Promise<any> {
    try {
      //refine the transcription
      const refined_transcription =
        await this.transcribeService.refineTranscription(raw_transcription);

      const audioRecord =
        await this.transcribeRepository.crateAudioTranscriptionLive({
          original_filename,
          encounter_id: encounterId,
          created_by: user_email,
          is_processed: true,
          duration,
          transcription_type,
          s3_uri,
          file_size,
          mime_type,
          raw_transcription,
          transcription: refined_transcription,
          detected_language: 'en-US',
          patient_id,
        });

      return audioRecord;
    } catch (error) {
      this.logger.error(`Error ending real-time transcription: ${error.stack}`);
    }
  }
}
