import { Injectable, NotFoundException, Logger, HttpStatus } from '@nestjs/common';
import { LetterHeadRepository } from './letterhead.repository';
import {
  UpdateLetterHeadDto,
  UpsertDoctorLetterheadSectionDto,
  upsertMultipleDoctorLetterheadSectionsDto,
} from './dto/letterhead.dto';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';
import { UtilsService } from '../common/utils/utils.service';

@Injectable()
export class LetterheadService {
  private readonly logger = new Logger(LetterheadService.name);

  constructor(
    private readonly letterheadrepository: LetterHeadRepository,
    private readonly prismaservice: PrismaService,
    private readonly s3fileuploadservice: s3FileUploadService,
    private readonly utilsService: UtilsService
  ) {}

  async getOrCreateLetterHead(userEmail: string) {
    return await this.letterheadrepository.getOrCreateLetterHead(userEmail);
  }

  async updateLetterHead(
    userEmail: string,
    model: UpdateLetterHeadDto,
    letterhead_id: string,
  ) {
    return await this.letterheadrepository.updateLetterHead(
      userEmail,
      model,
      letterhead_id,
    );
  }

  async getPagesizes(userEmail: string) {
    return await this.letterheadrepository.getAvailablePagesSizes(userEmail);
  }

  async getDoctorLetterheadSectionWithValues(userEmail: string) {
    return await this.letterheadrepository.getDoctorLetterheadSectionWithValues(
      userEmail,
    );
  }

  async getAllLetterheadSections() {
    return await this.letterheadrepository.getAllLetterheadSections();
  }

  async upsertMultipleDoctorLetterheadsections(
    userEmail: string,
    model: upsertMultipleDoctorLetterheadSectionsDto,
  ) {
    return await this.letterheadrepository.upsertMultipleDoctorLetterheadSections(
      userEmail,
      model,
    );
  }

  async toggleLetterhead(userEmail: string) {
    return await this.letterheadrepository.toggleDefaultLetterHead(userEmail);
  }

  async upsertImageDoctorLetterheadSection(
    userEamil: string,
    letterhead_id: string,
    letterhead_section_id: string,
    file: Express.Multer.File,
  ) {
    try {
      const existingFileUrl =
        await this.prismaservice.doctor_letterhead_section.findFirst({
          where: {
            letterhead_id: letterhead_id,
            letterhead_section_id: letterhead_section_id,
            isActive: true,
          },
          select: {
            value: true,
          },
        });

      if (existingFileUrl && existingFileUrl?.value) {
        const isDeleted = await this.s3fileuploadservice.deleteFileFromS3(
          existingFileUrl.value,
        );
        if (!isDeleted) {
          throw this.utilsService.formatErrorResponse(
            new Error('Failed to delete existing file'),
            'Error deleting existing letterhead file',
            HttpStatus.INTERNAL_SERVER_ERROR
          );
        }
      }

      const newFileUrl = await this.s3fileuploadservice.uploadFileToS3(
        file,
        `letterhead-${new Date().toISOString()}/${letterhead_id}/${letterhead_section_id}`,
      );

      const updatedValue =
        await this.letterheadrepository.upsertDoctorLetterHeadSections(
          userEamil,
          {
            letterhead_id: letterhead_id,
            letterhead_section_id: letterhead_section_id,
            value: newFileUrl,
          },
        );

      return updatedValue;
    } catch (error) {
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to upsert image doctor letterhead section',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async downloadLetterheadlogo(
    letterhead_id: string,
    letterhead_section_id: string,
  ) {
    try {
      const fileUrl =
        await this.prismaservice.doctor_letterhead_section.findFirst({
          where: {
            letterhead_id: letterhead_id,
            letterhead_section_id: letterhead_section_id,
            isActive: true,
          },
          select: {
            value: true,
          },
        });

      if (!fileUrl || !fileUrl?.value) {
        throw new NotFoundException('file not found');
      }

      const file = await this.s3fileuploadservice.downloadFileFromS3(
        fileUrl.value,
      );
      return file;
    } catch (error) {
      this.logger.error(`Error in downloadLetterheadlogo ${error}`, error?.stack);
      if (error instanceof NotFoundException) {
        throw this.utilsService.formatErrorResponse(
          error,
          'Letterhead logo file not found',
          HttpStatus.NOT_FOUND
        );
      }
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to download letterhead logo',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
