// src/encounter/__tests__/encounter.controller.spec.ts

import { Test, TestingModule } from '@nestjs/testing';
import { EncounterController } from '../encounter.controller';
import { EncounterService } from '../encounter.service';
import { Response } from 'express';
import { CreateEncounterDTO } from '../dtos/CreateEncounter.dto';
import { Gender } from '@prisma/client';

describe('EncounterController', () => {
  let controller: EncounterController;
  let service: jest.Mocked<EncounterService>;

  // Helper function to mock Express Response
  const mockResponse = (): Response => {
    const res: Partial<Response> = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };
    return res as Response;
  };

  // Define a mock for EncounterService with jest.Mocked<EncounterService>
  const mockEncounterService: Partial<jest.Mocked<EncounterService>> = {
    insertEncounter: jest.fn(),
    getEncounterById: jest.fn(),
    getAllEncounters: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EncounterController],
      providers: [
        {
          provide: EncounterService,
          useValue: mockEncounterService,
        },
      ],
    }).compile();

    controller = module.get<EncounterController>(EncounterController);
    service = module.get<EncounterService>(EncounterService) as jest.Mocked<EncounterService>;
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createEncounter', () => {
    it('should return 201 and created data on success', async () => {
      const response = mockResponse();
      const dto: CreateEncounterDTO = {
        patientFirstName: 'John',
        patientLastName: 'Doe',
        patientDOB: '1980-01-01',
        patientGender: 'MALE', // Input as string, validated by DTO
        patientPhone: '',
        phoneCountryCode: '',
      };
      const createdData = { encounterId: 'enc123', patientId: 'pat456' };

      // Mock the service method
      service.insertEncounter.mockResolvedValue(createdData);

      await controller.createEncounter(response, dto);

      expect(service.insertEncounter).toHaveBeenCalledWith(dto);
      expect(response.status).toHaveBeenCalledWith(201);
      expect(response.send).toHaveBeenCalledWith({
        statusCode: 201,
        message: "Encounter Created Successfully!",
        data: createdData
      });
    });

    it('should return 400 if insert returns falsy', async () => {
      const response = mockResponse();
      const dto: CreateEncounterDTO = {
        patientFirstName: 'John',
        patientLastName: 'Doe',
        patientDOB: '1980-01-01',
        patientGender: 'MALE',
        patientPhone: '',
        phoneCountryCode: '',
      };

      // Mock the service method to return null
      service.insertEncounter.mockResolvedValue(null);

      await controller.createEncounter(response, dto);

      expect(service.insertEncounter).toHaveBeenCalledWith(dto);
      expect(response.status).toHaveBeenCalledWith(400);
      expect(response.send).toHaveBeenCalledWith({
        statusCode: 400,
        message: "Encounter Not Created",
        data: { insertCount: null }
      });
    });
  });

  describe('getEncounterById', () => {
    it('should return 200 and encounter details', async () => {
      const response = mockResponse();
      const encounterId = 'uuid-encounter-1';
      const encounterData = {
        encounterId: encounterId,
        encounterDate: new Date('2020-01-01'),
        patient: {
          patientFirstName: 'John',
          patientLastName: 'Doe',
          patientGender: Gender.MALE, // Use enum
          patientDOB: new Date('1980-01-01'), // Date type
          patientPhone: '',
          phoneCountryCode: ''
        }
      };

      // Mock the service method
      service.getEncounterById.mockResolvedValue(encounterData);

      await controller.getEncounterById(encounterId, response);

      expect(service.getEncounterById).toHaveBeenCalledWith(encounterId);
      expect(response.status).toHaveBeenCalledWith(200);
      expect(response.send).toHaveBeenCalledWith({
        statusCode: 200,
        message: "Encounter Details fetched successfully",
        data: encounterData
      });
    });

    it('should return 200 and null data if not found', async () => {
      const response = mockResponse();
      const encounterId = 'non-existent';

      // Mock the service method to return null
      service.getEncounterById.mockResolvedValue(null);

      await controller.getEncounterById(encounterId, response);

      expect(service.getEncounterById).toHaveBeenCalledWith(encounterId);
      expect(response.status).toHaveBeenCalledWith(200);
      expect(response.send).toHaveBeenCalledWith({
        statusCode: 200,
        message: "Encounter Details fetched successfully",
        data: null
      });
    });
  });

  describe('getAllEncounters', () => {
    it('should use default values 1 and 10 if no query params are provided', async () => {
      const response = mockResponse();
      const fakeEncounters = [];

      // Mock the service method
      service.getAllEncounters.mockResolvedValue(fakeEncounters);

      // Since DefaultValuePipe is not applied in unit tests, simulate its behavior by passing default values
      await controller.getAllEncounters(1, 10, response);

      expect(service.getAllEncounters).toHaveBeenCalledWith(1, 10);
      expect(response.status).toHaveBeenCalledWith(200);
      expect(response.send).toHaveBeenCalledWith({
        statusCode: 200,
        message: "Encounters fetched successfully",
        data: fakeEncounters
      });
    });

    it('should use provided query params if given', async () => {
      const response = mockResponse();
      const fakeEncounters = [
        {
          encounterId: 'enc1',
          encounterDate: new Date('2021-01-01'),
          patient: {
            patientFirstName: 'John',
            patientLastName: 'Doe',
            patientGender: Gender.MALE,
            patientDOB: new Date('1980-01-01'),
            patientPhone: '**********',
            phoneCountryCode: '1'
          }
        }
      ];

      // Mock the service method
      service.getAllEncounters.mockResolvedValue(fakeEncounters);

      // Simulate passing page=2, limit=5
      await controller.getAllEncounters(2, 5, response);

      expect(service.getAllEncounters).toHaveBeenCalledWith(2, 5);
      expect(response.status).toHaveBeenCalledWith(200);
      expect(response.send).toHaveBeenCalledWith({
        statusCode: 200,
        message: "Encounters fetched successfully",
        data: fakeEncounters
      });
    });
  });
});
