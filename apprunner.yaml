version: 1.0

runtime: nodejs18          # managed Node.js 18 runtime (Amazon Linux 2023)

build:
  commands:
    build:
      - npm install            # faster, reproducible; npm install also fine
      - npm run build      # → dist/
run:
  pre-run:
    - dnf -y install xz tar
    - curl -L -o /tmp/ffmpeg.tar.xz https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz
    - tar -xf /tmp/ffmpeg.tar.xz -C /tmp
    - cp /tmp/ffmpeg-*-static/ffmpeg  /usr/bin/ffmpeg
    - cp /tmp/ffmpeg-*-static/ffprobe /usr/bin/ffprobe
    - chmod +x /usr/bin/ffmpeg /usr/bin/ffprobe
  command: npm run start:prod
  network:
    port: 3000
  env:
    - name: FFMPEG_PATH
      value: /usr/bin/ffmpeg
    - name: FFPROBE_PATH
      value: /usr/bin/ffprobe
    - name: AWS_REGION
      value: "us-east-1"
    - name: AWS_S3_BUCKET_NAME
      value: "ekkomd-prod-india"
    - name: AUTH0_ISSUER_URL
      value: "https://auth-dev.ekkomd.com/"
    - name: AUTH0_AUDIENCE
      value: "uma6kG995LqLc50v0zmY2zvSnhXlmy2v"
    - name: WHISPER_MODEL_PATH
      value: "https://api.openai.com/v1/audio/transcriptions"
    - name: GOOGLE_CLIENT_EMAIL
      value: "<EMAIL>"
    - name: GOOGLE_PROJECT_ID
      value: "ekko-transcription"
    - name: GOOGLE_CLIENT_ID
      value: "110054212904492596101"
    - name: GOOGLE_BUCKET_NAME
      value: "ekkomd"
  secrets:
    - name: AWS_ACCESS_KEY_ID
      value-from: "arn:aws:secretsmanager:us-east-1:************:secret:develop/ekkomd-core-service/aws-access-key-0wBNjE:AWS_ACCESS_KEY_ID::"
    - name: AWS_SECRET_ACCESS_KEY
      value-from: "arn:aws:secretsmanager:us-east-1:************:secret:develop/ekkomd-core-service/aws-access-key-0wBNjE:AWS_SECRET_ACCESS_KEY::"
    - name: DATABASE_URL
      value-from: "arn:aws:secretsmanager:us-east-1:************:secret:develop/postgres-core-db/rmata-db-url-xQvp8c:DATABASE_URL::"
    - name: OPENAI_API_KEY
      value-from: "arn:aws:secretsmanager:us-east-1:************:secret:develop/openai/api-key-PLrcDR:OPENAI_API_KEY::"
    - name: GOOGLE_PRIVATE_KEY
      value-from: "arn:aws:secretsmanager:us-east-1:************:secret:prod/gcp/private-key-wzPHgz:GCP_PRIVATE_KEY::"
    - name: GOOGLE_PRIVATE_KEY_ID
      value-from: "arn:aws:secretsmanager:us-east-1:************:secret:prod/gcp/private-key-wzPHgz:GCP_PRIVATE_KEY_ID::"