import { HttpStatus, Injectable, NotFoundException, Logger } from '@nestjs/common';
import { Subscription } from '@prisma/client';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { CreateSubscriptionDto } from './dto/subscription.dto';
import { isDate } from 'class-validator';
import { UtilsService } from 'src/common/utils/utils.service';
@Injectable()
export class SubscriptionRepository {
  private readonly logger = new Logger(SubscriptionRepository.name);

  constructor(private readonly prismaservice: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async getSubscriptionByDoctorEmail(
    doctorEmail: string,
  ): Promise<Subscription> {
    try {
      const subscription = await this.prismaservice.subscription.findFirst({
        where: {
          doctor: {
            isActive: true,
            email: doctorEmail,
          },
          isActive: true,
        },
        orderBy: {
          effectiveDateFrom: 'desc',
        },
      });
      return subscription;
    } catch (error) {
      this.logger.error('Error in getSubscriptionByDoctorEmail:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to get subscription by doctor email',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateSubscriptionStatusByIdAndUserId(
    subscription_id: string,
    userEmail: string,
    status: string,
  ): Promise<Subscription> {
    try {
      const subscription = await this.prismaservice.subscription.update({
        where: { subscriptionId: subscription_id, isActive: true },
        data: {
          status: status,
          updated_at: new Date(),
          updated_by: userEmail,
        },
      });
      return subscription;
    } catch (error) {
      this.logger.error('Error in updateSubscriptionStatusByIdAndUserId:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update subscription status',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async createSubscription(
    userEmail: string,
    model: CreateSubscriptionDto,
  ): Promise<Subscription> {
    try {
      const doctorExists = await this.prismaservice.doctor.findFirst({
        where: { email: model.email, isActive: true },
        select: {
          doctor_id: true,
        },
      });
      if (!doctorExists) {
        throw new NotFoundException(
          'doctor not found at the time of creating subscription',
        );
      }

      const subscription = await this.prismaservice.subscription.create({
        data: {
          planId: model.planId,
          doctor_id: doctorExists.doctor_id,
          effectiveDateTo:
            model.subscriptionEffectiveDateTo &&
            isDate(model.subscriptionEffectiveDateTo)
              ? model.subscriptionEffectiveDateTo
              : new Date(new Date().setMonth(new Date().getMonth() + 1)),
          created_by: userEmail,
        },
      });
      return subscription;
    } catch (error) {
      this.logger.error('Error in createSubscription:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
              'Failed to create subscription',
              HttpStatus.INTERNAL_SERVER_ERROR
              );
    }
  }
}
