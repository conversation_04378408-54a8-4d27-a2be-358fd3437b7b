import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { CreateNoteTypeDTO } from './dtos';
import { NoteSection, Prisma } from '@prisma/client';
import { PaginationResDTO } from 'src/common/dtos';
import { UtilsService } from '../common/utils/utils.service';

@Injectable()
export class NotesRepository {
  private readonly logger = new Logger(NotesRepository.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async findNoteHeader(params: {
    encounter_id: string;
    note_type_id: string;
    isActive: boolean;
  }) {
    return this.prisma.noteHeader.findFirst({
      where: {
        encounter_id: params.encounter_id,
        note_type_id: params.note_type_id,
        isActive: params.isActive,
      },
    });
  }

  async findNoteDetails(params: { note_header_id: string; isActive: boolean }) {
    return this.prisma.noteDetail.findMany({
      where: {
        note_header_id: params.note_header_id,
        isActive: params.isActive,
      },
      include: {
        note_section: {
          select: {
            section_name: true,
          },
        },
      },
    });
  }

  async getSectionName(sectionId: string): Promise<string> {
    const section = await this.prisma.noteSection.findUnique({
      where: {
        note_section_id: sectionId,
      },
      select: {
        section_name: true,
      },
    });
    return section?.section_name || '';
  }

  async findNoteType(noteTypeId: string) {
    try {
      return this.prisma.noteType.findUnique({
        where: {
          note_type_id: noteTypeId,
          isActive: true,
        },
        include: {
          note_type_sections: {
            where: {
              isActive: true,
            },
            include: {
              note_section: true,
            },
          },
        },
      });
    } catch (error) {
      this.logger.error('Error retrieving Note Type:', error);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve note type',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createNoteHeader(data: any) {
    try {
      return await this.prisma.noteHeader.create({ data });
    } catch (error) {
      this.logger.error('Error Creating Note header:', error);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create note header',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createNoteDetail(data: any) {
    try {
      return await this.prisma.noteDetail.create({ data });
    } catch (error) {
      this.logger.error('Error Creating Note detail:', error);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create note detail',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findEncounter(encounterId: string) {
    try {
      return this.prisma.encounter.findUnique({
        where: {
          encounter_id: encounterId,
          isActive: true,
        },
        include: {
          audio_transcriptions: {
            where: {
              isActive: true,
              is_processed: true,
              processing_error: null,
            },
            select: {
              id: true,
              transcription: true,
              transcription_type: true,
            },
          },
          patient: true,
          doctor: true,
        },
      });
    } catch (error) {
      this.logger.error('Error Finding Encounter:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to find encounter',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getNotesByEncounter(encounterId: string) {
    try {
      const notetypeid = await this.prisma.noteHeader.findFirst({
        where: { encounter_id: encounterId, isActive: true },
        select: {
          note_type_id: true,
        },
      });
      const notes = await this.prisma.noteHeader.findMany({
        where: {
          encounter_id: encounterId,
          isActive: true,
        },
        include: {
          note_details: {
            where: {
              isActive: true,
            },
            include: {
              note_section: {
                select: {
                  section_name: true,
                  note_section_id: true,
                  note_type_sections: {
                    where: {
                      note_type_id: notetypeid.note_type_id,
                      isActive: true,
                    },
                    select: {
                      order: true,
                    },
                  },
                },
              },
            },
          },
          note_type: true,
          doctor: true,
          patient: true,
        },
      });

      return notes.map((note) => ({
        encounterId: note.encounter_id,
        patientId: note.patient_id,
        doctorId: note.doctor_id,
        noteId: note.note_id,
        noteTypeId: note.note_type_id,
        noteTypeName: note.note_type.note_type_name,
        noteSections: note.note_details.map((detail) => ({
          noteDetailId: detail.note_detail_id,
          noteSectionId: detail.note_section.note_section_id,
          noteSectionName: detail.note_section.section_name,
          value: detail.value || '',
          order: detail.note_section.note_type_sections[0]?.order || 0,
        })),
      }));
    } catch (error) {
      this.logger.error(
        `Failed to fetch notes for encounter ${encounterId}:`,
        error,
      );
      throw error;
    }
  }

  // async createNoteTypeWithSections(
  //     createNoteTypeDto: CreateNoteTypeDTO,
  //     userEmail: string,
  // ) {
  //     const { note_type_name, sections } = createNoteTypeDto;
  //     try {
  //         return await this.prisma.noteType.create({
  //             data: {
  //                 note_type_name,
  //                 created_by: userEmail,
  //                 updated_by: null,
  //                 note_type_sections: {
  //                     create: sections.map((section) => ({
  //                         note_section: {
  //                             create: {
  //                                 section_name: section.section_name,
  //                                 created_by: userEmail,
  //                             },
  //                         },
  //                     })),
  //                 },
  //             },
  //             include: {
  //                 note_type_sections: {
  //                     include: {
  //                         note_section: true,
  //                     },
  //                 },
  //             },
  //         });
  //     } catch (error) {
  //         console.error('Error Creating note type:', error);
  //         throw error;
  //     }
  // }

  async createNoteTypeWithSections(
    createNoteTypeDto: CreateNoteTypeDTO,
    userEmail: string,
  ) {
    const { note_type_name, sections } = createNoteTypeDto;

    try {
      let data;
      await this.prisma.$transaction(
        async (prisma) => {
          // Create or get existing sections

          // Wait for all sections to be created/found
          const noteSections: { note_section_id: string; order: number }[] =
            await Promise.all(
              sections.map(async (section) => {
                try {
                  // Try to find existing section first
                  let existingSection = await prisma.noteSection.findUnique({
                    where: {
                      section_name: section.section_name,
                    },
                    select: {
                      note_section_id: true,
                    },
                  });

                  if (existingSection) {
                    return {
                      note_section_id: existingSection.note_section_id,
                      order: section.order,
                    };
                  }

                  // If not found, create new section
                  let newsection = await prisma.noteSection.create({
                    data: {
                      section_name: section.section_name,
                      created_by: userEmail,
                    },
                    select: {
                      note_section_id: true,
                    },
                  });
                  return {
                    note_section_id: newsection.note_section_id,
                    order: section.order,
                  };
                } catch (error) {
                  throw error;
                }
              }),
            );

          // Create the note type with connections to sections
          data = await prisma.noteType.create({
            data: {
              note_type_name,
              created_by: userEmail,
              updated_by: null,
              note_type_sections: {
                create: noteSections.map((section) => ({
                  created_by: userEmail,
                  order: section.order,
                  note_section: {
                    connect: {
                      note_section_id: section.note_section_id,
                    },
                  },
                })),
              },
            },
            select: {
              note_type_id: true,
              note_type_name: true,
              note_type_sections: {
                select: {
                  note_section: true,
                },
              },
            },
          });
        },
        {
          isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
          timeout: 300000,
        },
      );
      return data;
    } catch (error) {
      this.logger.error('Error Creating note type:', error);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create note type',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateNoteDetail(
    note_detail_id: string,
    note_section_id: string,
    data: { value?: string },
    userEmail: string,
  ) {
    try {
      const existingNote = await this.prisma.noteDetail.findFirst({
        where: {
          AND: [{ note_detail_id }, { note_section_id }, { isActive: true }],
        },
        include: {
          note_section: true,
        },
      });

      if (!existingNote) {
        this.logger.error(
          `Note detail not found for detail ID: ${note_detail_id} and section ID: ${note_section_id}`,
        );
        throw new NotFoundException('Note detail not found');
      }

      const validSection = await this.prisma.noteTypeSection.findFirst({
        where: {
          note_section_id,
          isActive: true,
        },
      });

      if (!validSection) {
        this.logger.error(
          `Invalid note section for section ID: ${note_section_id}`,
        );
        throw new BadRequestException(
          'Invalid note section for this note type',
        );
      }

      const updatedNote = await this.prisma.noteDetail.update({
        where: { note_detail_id },
        data: {
          value: data.value,
          note_section_id,
          updated_at: new Date(),
          updated_by: userEmail,
        },
        include: {
          note_section: true,
        },
      });

      return {
        noteSectionId: updatedNote.note_section.note_section_id,
        noteSectionName: updatedNote.note_section.section_name,
        value: updatedNote.value || '',
      };
    } catch (error) {
      this.logger.error('Error updating note detail:', error?.stack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw this.utilsService.formatErrorResponse(
          error,
          error.message,
          error instanceof NotFoundException
            ? HttpStatus.NOT_FOUND
            : HttpStatus.BAD_REQUEST,
        );
      }
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update note detail',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteNoteDetail(
    note_detail_id: string,
    note_section_id: string,
    userEmail: string,
  ) {
    try {
      if (!note_detail_id) {
        this.logger.error('Note detail ID is required but was not provided');
        throw new BadRequestException('note_detail_id is required');
      }

      const existingNote = await this.prisma.noteDetail.findFirst({
        where: {
          AND: [{ note_detail_id }, { note_section_id }, { isActive: true }],
        },
        include: {
          note_section: true,
        },
      });

      if (!existingNote) {
        this.logger.error(
          `Note detail not found for detail ID: ${note_detail_id} and section ID: ${note_section_id}`,
        );
        throw new NotFoundException('Note detail not found');
      }

      await this.prisma.noteDetail.update({
        where: { note_detail_id },
        data: {
          isActive: false,
          updated_at: new Date(),
          updated_by: userEmail,
        },
      });

      return {
        noteSectionId: existingNote.note_section.note_section_id,
        noteSectionName: existingNote.note_section.section_name,
      };
    } catch (error) {
      console.error('Error deleting note detail:', error?.stack);
      if (error instanceof NotFoundException) {
        throw this.utilsService.formatErrorResponse(
          error,
          error.message,
          HttpStatus.NOT_FOUND,
        );
      }
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete note detail',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteNotesForEncounter(encounterId: string, userEmail: string) {
    try {
      // Begin transaction since we're making multiple related deletions
      const result = await this.prisma.$transaction(async (tx) => {
        // 1. Find all note headers associated with the encounter
        const noteHeaders = await tx.noteHeader.findMany({
          where: {
            encounter_id: encounterId,
            isActive: true,
          },
          select: {
            note_id: true,
          },
        });

        // Get array of note header IDs
        const noteHeaderIds = noteHeaders.map((header) => header.note_id);

        // 2. If there are associated note headers, delete their details first
        if (noteHeaderIds.length > 0) {
          await tx.noteDetail.updateMany({
            where: {
              note_header_id: {
                in: noteHeaderIds,
              },
            },
            data: {
              isActive: false,
              updated_at: new Date(),
              updated_by: userEmail,
            },
          });

          // 3. Soft delete the note headers
          await tx.noteHeader.updateMany({
            where: {
              note_id: {
                in: noteHeaderIds,
              },
            },
            data: {
              isActive: false,
              updated_at: new Date(),
              updated_by: userEmail,
            },
          });
        }

        return {
          deletedNoteHeadersCount: noteHeaderIds.length,
          deletedNoteDetailsCount:
            noteHeaderIds.length > 0
              ? await tx.noteDetail.count({
                  where: {
                    note_header_id: {
                      in: noteHeaderIds,
                    },
                  },
                })
              : 0,
        };
      });

      return {
        success: true,
        message: 'Notes associated with encounter deleted successfully',
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to delete notes associated with encounter',
        error: error.message,
      };
    }
  }

  async getAllNoteTypesWithSections(doctorEmail: string) {
    try {
      const noteTypes = await this.prisma.noteType.findMany({
        where: {
          isActive: true,
          OR: [{ created_by: 'ADMIN' }, { created_by: doctorEmail }],
        },
        select: {
          note_type_id: true,
          note_type_name: true,
          note_type_sections: {
            select: {
              order: true,
              note_section: {
                select: {
                  note_section_id: true,
                  section_name: true,
                },
              },
            },
          },
        },
      });

      return noteTypes;
      //   .map((noteType) => ({
      //     noteTypeId: noteType.note_type_id,
      //     noteTypeName: noteType.note_type_name,
      //     sections: noteType.note_type_sections.map((section) => ({
      //       sectionId: section.note_section.note_section_id,
      //       sectionName: section.note_section.section_name,
      //     })),
      //   }));
    } catch (error) {
      this.logger.error(
        'Error retrieving all Note Types with Sections:',
        error?.stack,
      );

      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve all note types with sections',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllNoteSections(doctorEmail: string, page: number, limit: number) {
    try {
      const totalRecords: number = await this.prisma.noteSection.count({
        where: {
          isActive: true,
          OR: [{ created_by: 'ADMIN' }, { created_by: doctorEmail }],
        },
      });

      let totalPages: number = 0;
      if (totalRecords && totalRecords > 0) {
        totalPages = Math.ceil(totalRecords / limit);
      }

      const pagination: PaginationResDTO = {
        currentPage: page,
        recordsPerPage: limit,
        totalRecords: totalRecords ? totalRecords : 0,
        totalPages: totalPages,
      };

      const noteSections = await this.prisma.noteSection.findMany({
        where: {
          isActive: true,
          OR: [{ created_by: 'ADMIN' }, { created_by: doctorEmail }],
        },
        select: {
          note_section_id: true,
          section_name: true,
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      return {
        statusCode:
          noteSections.length > 0 ? HttpStatus.OK : HttpStatus.NO_CONTENT,
        message:
          noteSections.length > 0
            ? 'all note sections fetched successfully'
            : 'no section found',
        data: noteSections,
        pagination: pagination,
      };
    } catch (error) {
      this.logger.error('Error retrieving all Note Sections:', error?.stack);
      throw error;
    }
  }
}
