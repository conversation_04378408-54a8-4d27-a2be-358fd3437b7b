-- AlterTable
ALTER TABLE "encounter_status" ALTER COLUMN "description" DROP NOT NULL;

-- AlterTable
ALTER TABLE "status" ALTER COLUMN "description" DROP NOT NULL;

-- CreateTable
CREATE TABLE "response_type" (
    "type_name" TEXT NOT NULL,
    "label" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "response_type_pkey" PRIMARY KEY ("type_name")
);

-- CreateTable
CREATE TABLE "ResponseLLM" (
    "responseId" UUID NOT NULL,
    "raw_response" TEXT NOT NULL,
    "successful" BOOLEAN NOT NULL DEFAULT false,
    "transcriptionId" UUID,
    "response_type" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "ResponseLLM_pkey" PRIMARY KEY ("responseId")
);

-- AddForeignKey
ALTER TABLE "ResponseLLM" ADD CONSTRAINT "ResponseLLM_transcriptionId_fkey" FOREIGN KEY ("transcriptionId") REFERENCES "audio_transcription"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResponseLLM" ADD CONSTRAINT "ResponseLLM_response_type_fkey" FOREIGN KEY ("response_type") REFERENCES "response_type"("type_name") ON DELETE SET NULL ON UPDATE CASCADE;
