import { Injectable } from '@nestjs/common';
import { UniversalPicklistRepository } from './universal-picklist.repository';

@Injectable()
export class UniversalPicklistService {
    constructor(
        private readonly universalPicklistRepository: UniversalPicklistRepository,
    ) {}

    async GetUniversalPicklistData({
        master_code,
        parent_record_id,
        domain,
    }: {
        master_code: string;
        parent_record_id: string[];
        domain?: string;
    }): Promise<any> {
        return await this.universalPicklistRepository.GetUniversalPicklistData({
            master_code,
            parent_record_id,
            domain,
        });
        
    }

}
