-- CreateTable
CREATE TABLE "tbl_vital_types" (
    "vital_type_id" UUID NOT NULL,
    "unit_name" TEXT,
    "max_value" TEXT,
    "min_value" TEXT,
    "measurement_system" TEXT,
    "vital_type_name" TEXT,
    "data_type" TEXT,
    "display_name" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_vital_types_pkey" PRIMARY KEY ("vital_type_id")
);

-- CreateTable
CREATE TABLE "tbl_vital_measurements" (
    "vital_measurement_id" UUID NOT NULL,
    "vital_type_id" UUID NOT NULL,
    "encounter_id" UUID NOT NULL,
    "vital_value" TEXT,
    "measurement_time" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_vital_measurements_pkey" PRIMARY KEY ("vital_measurement_id")
);

-- AddForeignKey
ALTER TABLE "tbl_vital_measurements" ADD CONSTRAINT "tbl_vital_measurements_vital_type_id_fkey" FOREIGN KEY ("vital_type_id") REFERENCES "tbl_vital_types"("vital_type_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_vital_measurements" ADD CONSTRAINT "tbl_vital_measurements_encounter_id_fkey" FOREIGN KEY ("encounter_id") REFERENCES "encounter"("encounter_id") ON DELETE RESTRICT ON UPDATE CASCADE;
