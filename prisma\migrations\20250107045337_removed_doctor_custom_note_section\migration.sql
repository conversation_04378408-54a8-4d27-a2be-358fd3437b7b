/*
  Warnings:

  - You are about to drop the `doctor_custom_note` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `doctor_custom_note_section` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `doctor_custom_section` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "doctor_custom_note" DROP CONSTRAINT "doctor_custom_note_doctor_id_fkey";

-- DropForeignKey
ALTER TABLE "doctor_custom_note" DROP CONSTRAINT "doctor_custom_note_encounter_id_fkey";

-- DropForeignKey
ALTER TABLE "doctor_custom_note" DROP CONSTRAINT "doctor_custom_note_patient_id_fkey";

-- DropForeignKey
ALTER TABLE "doctor_custom_note_section" DROP CONSTRAINT "doctor_custom_note_section_custom_note_id_fkey";

-- DropForeignKey
ALTER TABLE "doctor_custom_note_section" DROP CONSTRAINT "doctor_custom_note_section_custom_section_id_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "doctor_custom_section" DROP CONSTRAINT "doctor_custom_section_doctor_id_fkey";

-- DropTable
DROP TABLE "doctor_custom_note";

-- DropTable
DROP TABLE "doctor_custom_note_section";

-- DropTable
DROP TABLE "doctor_custom_section";
