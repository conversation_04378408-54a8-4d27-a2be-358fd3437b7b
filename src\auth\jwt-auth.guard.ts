import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { ExecutionContext } from '@nestjs/common';
import { IS_PUBLIC_KEY } from '../common/decorators/public-endpoint.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext) {
    // Check if the route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) {
      return true; // Allow access without authentication
    }
    return super.canActivate(context); // Proceed with JWT authentication
  }

  handleRequest(err, user, info, _context: ExecutionContext) {
    if (err || !user) {
      let message = 'Unauthorized';

      if (info) {
        switch (info.name) {
          case 'TokenExpiredError':
            message = 'Token has expired';
            break;
          case 'JsonWebTokenError':
            message = 'Invalid token';
            break;
          case 'NotBeforeError':
            message = 'Token not active yet';
            break;
          default:
            message = info.message || 'Unauthorized';
        }
      }

      this.logger.warn(`Authentication failed: ${message}`);

      throw new UnauthorizedException(message);
    }
    return user;
  }
}
