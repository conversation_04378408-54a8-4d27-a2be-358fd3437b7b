import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Gender } from '@prisma/client';
import {
  IsAlpha,
  IsEnum,
  IsNotEmpty,
  IsPhoneNumber,
  IsString,
  MaxLength,
  IsDateString,
  IsOptional,
  Matches,
} from 'class-validator';

export class CreateEncounterDTO {
  @ApiProperty({
    description: 'First name of the patient',
    example: 'Alice',
  })
  @IsNotEmpty({ message: 'Patient first name is required' })
  @Matches(/^[A-Za-z]+(?:\s+[A-Za-z]+)*$/, {
    message:
      'Patient first name must contain letters and may include spaces between words',
  })
  patientFirstName: string;

  @ApiProperty({
    description: 'Last name of the patient',
    example: '<PERSON>',
  })
  // @Matches(/^[A-Za-z]+(?:\s+[A-Za-z]+)*$/, {
  //   message:
  //     'Patient last name must contain letters and may include spaces between words',
  // })
  patientLastName?: string;

  @ApiProperty({
    description: 'Gender of the patient',
    enum: Gender,
    example: Gender.FEMALE,
  })
  @IsNotEmpty({ message: 'Patient gender is required' })
  @IsEnum(Gender, {
    message: `Patient gender must be one of: ${Object.values(Gender).join(', ')}`,
  })
  patientGender: Gender;

  @ApiProperty({
    description: 'Date of birth of the patient in ISO 8601 format',
    example: '1990-01-01',
    type: String,
    format: 'date',
  })
  @IsNotEmpty({ message: 'Patient date of birth is required' })
  @IsDateString(
    {},
    { message: 'Patient date of birth must be a valid ISO 8601 date string' },
  )
  patientDOB: string;

  @ApiPropertyOptional({
    description: "Patient's phone number in E.164 format",
    example: '+919876543211',
  })
  @MaxLength(20, {
    message: 'Patient phone number must not exceed 20 characters',
  })
  patientPhone?: string;

  @ApiPropertyOptional({
    description: "Country code of the patient's phone number",
    example: '+91',
    maxLength: 5,
  })
  @IsString({ message: 'Phone country code must be a string' })
  @MaxLength(5, { message: 'Phone country code must not exceed 5 characters' })
  phoneCountryCode?: string;

  @ApiPropertyOptional({
    description: 'Note type id of the selected not type on encounter popup',
    example: '311e9e7f-83c8-421e-997c-c8fe9810995d',
  })
  @IsString({ message: 'Note type id must be a string' })
  note_type_id: string;

  @ApiPropertyOptional({
    description: 'Patient id of the selected patient on encounter popup',
    example: '311e9e7f-83c8-421e-997c-c8fe9810995d',
  })
  @IsOptional()
  @IsString({ message: 'Patient id must be a string' })
  patient_id: string;
}
