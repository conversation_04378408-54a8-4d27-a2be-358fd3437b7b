/*
  Warnings:

  - You are about to drop the `ResponseLLM` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "ResponseLLM" DROP CONSTRAINT "ResponseLLM_response_type_fkey";

-- DropForeignKey
ALTER TABLE "ResponseLLM" DROP CONSTRAINT "ResponseLLM_transcriptionId_fkey";

-- DropTable
DROP TABLE "ResponseLLM";

-- CreateTable
CREATE TABLE "response_llm" (
    "responseId" UUID NOT NULL,
    "raw_response" TEXT NOT NULL,
    "successful" BOOLEAN NOT NULL DEFAULT false,
    "transcriptionId" UUID,
    "response_type" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "response_llm_pkey" PRIMARY KEY ("responseId")
);

-- AddForeignKey
ALTER TABLE "response_llm" ADD CONSTRAINT "response_llm_transcriptionId_fkey" FOREIGN KEY ("transcriptionId") REFERENCES "audio_transcription"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "response_llm" ADD CONSTRAINT "response_llm_response_type_fkey" FOREIGN KEY ("response_type") REFERENCES "response_type"("type_name") ON DELETE SET NULL ON UPDATE CASCADE;
