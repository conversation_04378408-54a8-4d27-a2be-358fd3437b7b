/**
 * Seeder for tbl_org_setting
 * @param {import('@prisma/client').PrismaClient} prisma
 */



module.exports = async function (prisma) {
    const settings = [
        // ---------------- Note Settings ----------------
        {
            key: 'note.default_type',
            value: '87ab7699-8c22-4471-9975-f9d1337f4e2e',
            description: 'SOAP Note',
            config_type: 'USER_SETTING',
            data_type: 'string',
        },
        {
            key: 'note.icd10_codes',
            value: 'false',
            description: 'Enable automatic suggestions of ICD-10 codes while documenting notes.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'note.allow_non_clinical_view',
            value: 'false',
            description: 'Decide whether front-desk or administrative staff can view patient visit notes.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },

        // ---------------- Labs Settings ----------------
        {
            key: 'labs.preferred_vendor',
            value: '',
            description: 'Select your default lab partner for test orders.',
            config_type: 'USER_SETTING',
            data_type: 'string',
        },
        {
            key: 'labs.suggested_labs',
            value: 'false',
            description: 'Enable system-recommended lab tests based on patient symptoms.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'labs.allow_non_clinical_view',
            value: 'false',
            description: 'Decide whether front-desk or administrative staff can view patient visit notes.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },

        // ---------------- Prescription Settings ----------------
        {
            key: 'prescription.preferred_brand',
            value: '',
            description: 'Set the default brand to auto-populate when prescribing medications.',
            config_type: 'USER_SETTING',
            data_type: 'string',
        },
        {
            key: 'prescription.allow_non_clinical_view',
            value: 'false',
            description: 'Decide whether non-clinical staff can view prescribed medications.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },

        // ---------------- Vitals Settings ----------------
        {
            key: 'vitals.capture.temperature',
            value: 'true',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.blood_pressure.systolic',
            value: 'true',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.blood_pressure.diastolic',
            value: 'true',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.heart_rate',
            value: 'true',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.respiratory_rate',
            value: 'true',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.oxygen_saturation',
            value: 'false',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.height',
            value: 'false',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.weight',
            value: 'false',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.pain_score',
            value: 'false',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.bmi',
            value: 'false',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.random_blood_glucose',
            value: 'false',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.fasting_blood_glucose',
            value: 'false',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.capture.postprandial_blood_glucose',
            value: 'false',
            description: '',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'vitals.units',
            value: 'Metric',
            description: 'Choose your preferred measurement system for vitals.',
            config_type: 'USER_SETTING',
            data_type: 'string',
        },
        {
            key: 'vitals.allow_non_clinical_view',
            value: 'false',
            description: 'Decide whether non-clinical staff can view prescribed medications.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },

        // ---------------- Context Settings ----------------
        {
            key: 'context.allow_non_clinical_manage',
            value: 'false',
            description: 'Decide whether your non-clinical staff can manage context.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },

        // ---------------- Export Settings ----------------
        {
            key: 'export.default_language',
            value: 'English',
            description: 'Select the language for exported notes, prescriptions, and reports.',
            config_type: 'USER_SETTING',
            data_type: 'string',
        },
        {
            key: 'export.allow_non_clinical_export',
            value: 'false',
            description: 'Decide whether your non-clinical staff can download or export patient notes.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'export.default.notes',
            value: 'false',
            description: 'Choose which sections are included by default in exports.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'export.default.labs',
            value: 'false',
            description: 'Choose which sections are included by default in exports.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'export.default.prescriptions',
            value: 'false',
            description: 'Choose which sections are included by default in exports.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'export.default.vitals',
            value: 'false',
            description: 'Choose which sections are included by default in exports.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
        {
            key: 'export.default.patient_advice',
            value: 'false',
            description: 'Choose which sections are included by default in exports.',
            config_type: 'USER_SETTING',
            data_type: 'boolean',
        },
    ];

    for (const setting of settings) {
        await prisma.tbl_org_setting.upsert({
            where: { key: setting.key },
            update: {
                value: setting.value,
                description: setting.description,
                config_type: setting.config_type,
                data_type: setting.data_type,
                updated_by: 'SYSTEM',
            },
            create: {
                key: setting.key,
                value: setting.value,
                description: setting.description,
                config_type: setting.config_type,
                data_type: setting.data_type,
                created_by: 'SYSTEM',
            },
        });
    }

    console.log('✅ Org settings seeded successfully');
}


