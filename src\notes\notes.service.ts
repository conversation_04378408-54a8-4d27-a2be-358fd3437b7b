import { Injectable, Logger, HttpStatus, HttpException } from '@nestjs/common';
import axios from 'axios';
import { CreateNoteTypeDTO } from './dtos';
import { NotesRepository } from './notes.repository';
import { CreateNoteSectionDto } from './dtos/CreateNotes.dto';
import {
  LLM_RESPONSETYPES,
  TRANSCRIPTION_TYPES,
} from 'src/common/constants/common.constants';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class NotesService {
  private readonly logger = new Logger(NotesService.name);

  constructor(
    private readonly notesRepository: NotesRepository,
    private readonly utilsService: UtilsService,
  ) {}

  // private async fetchNoteSections(noteTypeId: string) {
  //   try {
  //     const noteType = await this.notesRepository.findNoteType(noteTypeId);
  //     if (!noteType) {
  //       throw new Error('Note type not found');
  //     }

  //     return noteType.note_type_sections.map((section) => ({
  //       type: noteType.note_type_name,
  //       name: section.note_section.section_name,
  //       id: section.note_section.note_section_id,
  //     }));
  //   } catch (error) {
  //     this.logger.error('Error fetching note sections:', error.message);
  //     throw new Error(`Failed to fetch note sections: ${error.message}`);
  //   }
  // }

  // async generateNotesFromTranscription(
  //   encounterId: string,
  //   noteTypeId: string,
  //   userEmail: string,
  //   extraNoteTypes?: CreateNoteSectionDto[]
  // ) {
  //   this.logger.log(
  //     `Generating notes for encounter: ${encounterId}, note type: ${noteTypeId}`,
  //   );

  //   try {
  //     // Check for existing notes
  //     const existingNoteHeader = await this.notesRepository.findNoteHeader({
  //       encounter_id: encounterId,
  //       note_type_id: noteTypeId,
  //       isActive: true,
  //     });

  //     if (existingNoteHeader) {
  //       const existingNoteDetails = await this.notesRepository.findNoteDetails({
  //         note_header_id: existingNoteHeader.note_id,
  //         isActive: true,
  //       });

  //       // Convert to the expected format
  //       const generatedNote = {};
  //       await Promise.all(
  //         existingNoteDetails.map(async (detail) => {
  //           if (detail.note_section?.section_name) {
  //             generatedNote[detail.note_section.section_name] = detail.value;
  //           }
  //         })
  //       );

  //       return {
  //         noteHeader: existingNoteHeader,
  //         noteDetails: existingNoteDetails,
  //         generatedNote,
  //       };
  //     }

  //     // If no existing notes found, proceed with generating new ones
  //     const encounter = await this.notesRepository.findEncounter(encounterId);

  //     if (!encounter) {
  //       this.logger.warn('Encounter not found or inactive.');
  //       throw new Error('Encounter not found or inactive');
  //     }

  //     const combinedTranscription = encounter.audio_transcriptions
  //       .map((t) => t.transcription)
  //       .filter((t) => t)
  //       .join(' ');

  //     if (!combinedTranscription) {
  //       this.logger.warn('No valid transcriptions found.');
  //       throw new Error('No valid transcriptions found');
  //     }

  //     const sections = await this.fetchNoteSections(noteTypeId);
  //     const { sections: generatedNote, sectionMetadata } =
  //       await this.generateSoapNote(combinedTranscription, noteTypeId, sections);

  //     // Create note header
  //     const noteHeader = await this.notesRepository.createNoteHeader({
  //       patient_id: encounter.patient_id,
  //       encounter_id: encounter.encounter_id,
  //       doctor_id: encounter.doctor_id,
  //       note_type_id: noteTypeId,
  //       created_by: userEmail,
  //       isActive: true,
  //     });

  //     // Create note details
  //     const noteDetails = await Promise.all(
  //       sectionMetadata.map(async (section) => {
  //         return await this.notesRepository.createNoteDetail({
  //           note_header_id: noteHeader.note_id,
  //           note_section_id: section.id,
  //           value: generatedNote[section.name] || 'No content extracted',
  //           created_by: userEmail,
  //           isActive: true,
  //         });
  //       }),
  //     );

  //     return {
  //       noteHeader,
  //       noteDetails,
  //       generatedNote,
  //     };
  //   } catch (error) {
  //     this.logger.error('Error generating notes:', error.message);
  //     throw new Error(`Failed to generate notes: ${error.message}`);
  //   }
  // }

  private async fetchNoteSections(noteTypeId: string) {
    try {
      const noteType = await this.notesRepository.findNoteType(noteTypeId);
      if (!noteType) {
        throw this.utilsService.formatErrorResponse(
          new Error('Note type not found'),
          'Note type not found',
          HttpStatus.NOT_FOUND
        );
      }

      return noteType.note_type_sections.map((section) => ({
        type: noteType.note_type_name,
        name: section.note_section.section_name,
        id: section.note_section.note_section_id,
      }));
    } catch (error) {
      this.logger.error('Error fetching note sections:', error.message);
      throw this.utilsService.formatErrorResponse(
        error,
        `Failed to fetch note sections`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async generateNotesFromTranscription(
    encounterId: string,
    noteTypeId: string,
    userEmail: string,
    extraSections?: CreateNoteSectionDto[],
  ) {
    this.logger.log(
      `Generating notes for encounter: ${encounterId}, note type: ${noteTypeId}`,
    );

    try {
      // Fetch encounter early to make it available throughout the function
      const encounter = await this.notesRepository.findEncounter(encounterId);

      if (!encounter) {
        this.logger.warn('Encounter not found or inactive.');
        throw new Error('Encounter not found or inactive');
      }

      const combinedTranscription = encounter.audio_transcriptions
        .filter((t) => t.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL)
        .map((t) => t.transcription)
        .filter((t) => t)
        .join(' ');

      if (!combinedTranscription) {
        this.logger.warn('No valid transcriptions found.');
        throw new Error('No valid transcriptions found');
      }

      // Check for existing notes
      const existingNoteHeader = await this.notesRepository.findNoteHeader({
        encounter_id: encounterId,
        note_type_id: noteTypeId,
        isActive: true,
      });

      if (existingNoteHeader) {
        const existingNoteDetails = await this.notesRepository.findNoteDetails({
          note_header_id: existingNoteHeader.note_id,
          isActive: true,
        });

        // Convert to the expected format
        const generatedNote = {};
        await Promise.all(
          existingNoteDetails.map(async (detail) => {
            if (detail.note_section?.section_name) {
              generatedNote[detail.note_section.section_name] = detail.value;
            }
          }),
        );
        
        

        // If extra sections are provided, check which ones don't exist
        if (extraSections && extraSections.length > 0) {
          const existingSectionNames = existingNoteDetails.map(
            (detail) => detail.note_section?.section_name,
          );

          const missingExtraSections = extraSections.filter(
            (section) => !existingSectionNames.includes(section.section_name),
          );

          if (missingExtraSections.length > 0) {
            // Generate only the missing sections
            const { sections: newSections } = await this.generateSoapNote(
              combinedTranscription,
              noteTypeId,
              missingExtraSections.map((section) => ({
                type: 'Additional Section',
                name: section.section_name,
                id: section.section_id,
              })),
              encounter.audio_transcriptions
                .filter(
                  (t) => t.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL,
                )
                .map((t) => t.id),
              userEmail,
            );

            // Create note details for missing sections
            const newNoteDetails = await Promise.all(
              missingExtraSections.map(async (section) => {
                return await this.notesRepository.createNoteDetail({
                  note_header_id: existingNoteHeader.note_id,
                  note_section_id: section.section_id,
                  value: this.getSectionValueBySectionName(
                    newSections,
                    section.section_name,
                  ),
                  created_by: userEmail,
                  isActive: true,
                });
              }),
            );

            // Merge with existing notes
            return {
              noteHeader: existingNoteHeader,
              noteDetails: [...existingNoteDetails, ...newNoteDetails],
              generatedNote: { ...generatedNote, ...newSections },
            };
          }
        }

        return {
          noteHeader: existingNoteHeader,
          noteDetails: existingNoteDetails,
          generatedNote,
        };
      } else {
        await this.notesRepository.deleteNotesForEncounter(
          encounterId,
          userEmail,
        );
      }

      const sections = await this.fetchNoteSections(noteTypeId);

      // If extra sections are provided, add them to the sections array
      if (extraSections && extraSections.length > 0) {
        extraSections.map((sec) => {
          let ix = sections.findIndex((s) => s.id === sec.section_id);
          if (ix < 0) {
            sections.push({
              type: 'Additional Section',
              name: sec.section_name,
              id: sec.section_id,
            });
          }
        });
      }

      const { sections: generatedNote, sectionMetadata } =
        await this.generateSoapNote(
          combinedTranscription,
          noteTypeId,
          sections,
          encounter.audio_transcriptions
            .filter(
              (t) => t.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL,
            )
            .map((t) => t.id),
          userEmail,
        );

      // Create note header
      const noteHeader = await this.notesRepository.createNoteHeader({
        patient_id: encounter.patient_id,
        encounter_id: encounter.encounter_id,
        doctor_id: encounter.doctor_id,
        note_type_id: noteTypeId,
        created_by: userEmail,
        isActive: true,
      });

      // Create note details
      const noteDetails = await Promise.all(
        sectionMetadata.map(async (section) => {
          return await this.notesRepository.createNoteDetail({
            note_header_id: noteHeader.note_id,
            note_section_id: section.id,
            value: this.getSectionValueBySectionName(
              generatedNote,
              section.name,
            ),
            created_by: userEmail,
            isActive: true,
          });
        }),
      );

      return {
        noteHeader,
        noteDetails,
        generatedNote,
      };
    } catch (error) {
      this.logger.error('Error generating notes:', error.message);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to generate notes from transcription',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private getSectionValueBySectionName(
    noteSections: string[],
    sectionName: string,
  ) {
    const foundSection = noteSections[sectionName];

    if (foundSection == null) {
      return 'No content extracted';
    }

    if (Array.isArray(foundSection)) {
      return foundSection.join(', ');
    }

    return foundSection;
  }

  private async generateSoapNote(
    refinedText: string,
    noteTypeId: string,
    sections: any[],
    transcription_ids: string[],
    userEmail: string,
  ): Promise<any> {
    this.logger.log('Generating SOAP note...');
    try {
      if (!sections || sections.length === 0) {
        throw this.utilsService.formatErrorResponse(
          new Error('No sections available'),
          'No sections available for the given note type ID',
          HttpStatus.BAD_REQUEST
        );
      }

      const sectionNames = sections.map((s) => s.name).join(', ');

      const prompt = `${refinedText} Based on the Transcript, Generate a ${sections[0].type} Note with the following sections: ${sectionNames}. Only give me the note with the section heading and the generated content below it. If there is no information in the transcript about a particular section, return "No information recorded" under the section. Respond in pure javascript JSON format that can be parsed with a key 'response' containing the pure text, no Markdown, no special characters`;

      const response = await this.utilsService.genereteResponseFromLLM(
        'gpt-4o',
        'user',
        prompt,
        1500,
        0.7,
        LLM_RESPONSETYPES.JSON,
        'NOTES',
        transcription_ids,
        userEmail,
      );
      this.logger.log('Note generated successfully.');

      const rawNote = response || '';
      if (!rawNote) {
        throw this.utilsService.formatErrorResponse(
          new Error('Invalid AI response'),
          'The AI response did not contain valid content',
          HttpStatus.BAD_REQUEST
        );
      }

      let parsedJson: any;
      try {
        parsedJson = JSON.parse(rawNote);
      } catch (error) {
        this.logger.error('Failed to parse JSON string:', error);
        return {};
      }

      if (!parsedJson?.response) {
        this.logger.warn('Invalid note format: missing response object');
        return {};
      }

      const parsedSections: Record<string, string> = {};
      for (const section of sections) {
        const sectionValue = parsedJson.response[section.name.trim()];
        parsedSections[section.name] = sectionValue || 'Not available';
      }

      return {
        sections: parsedSections,
        sectionMetadata: sections,
      };
    } catch (error) {
      this.logger.error('Error generating note:', error.message);
      if (error instanceof HttpException) {
        throw error; // If it's already our formatted error, just rethrow
      }
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to generate note',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getNotesByEncounter(encounterId: string) {
    this.logger.log(`Fetching notes for encounter: ${encounterId}`);
    const notes = await this.notesRepository.getNotesByEncounter(encounterId);
    return notes;
  }

  async createNoteTypeWithSections(
    createNoteTypeDTO: CreateNoteTypeDTO,
    userEmail: string,
  ) {
    return await this.notesRepository.createNoteTypeWithSections(
      createNoteTypeDTO,
      userEmail,
    );
  }

  async updateNoteDetail(
    note_detail_id: string,
    note_section_id: string,
    data: { value?: string },
    userEmail: string,
  ) {
    try {
      const updateNote = await this.notesRepository.updateNoteDetail(
        note_detail_id,
        note_section_id,
        data,
        userEmail,
      );
      return updateNote;
    } catch (error) {
      this.logger.error('Error update notes:', error.message);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update notes',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async deleteNoteDetail(
    note_detail_id: string,
    note_section_id: string,
    userEmail: string,
  ) {
    try {
      const updateNote = await this.notesRepository.deleteNoteDetail(
        note_detail_id,
        note_section_id,
        userEmail,
      );
      return updateNote;
    } catch (error) {
      this.logger.error('Error delete notes:', error.message);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete notes',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getAllNoteTypesWithSections(doctorEmail: string) {
    return await this.notesRepository.getAllNoteTypesWithSections(doctorEmail);
  }

  async getAllNoteSections(
    doctorEmail: string,
    page: number = 0,
    limit: number = 10,
  ) {
    return await this.notesRepository.getAllNoteSections(
      doctorEmail,
      page,
      limit,
    );
  }
}
