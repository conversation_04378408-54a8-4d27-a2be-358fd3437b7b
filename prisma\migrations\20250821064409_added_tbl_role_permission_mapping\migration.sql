/*
  Warnings:

  - The values [user,system] on the enum `PermissionType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "PermissionType_new" AS ENUM ('USER_DEFINED', 'SYSTEM_DEFINED');
ALTER TYPE "PermissionType" RENAME TO "PermissionType_old";
ALTER TYPE "PermissionType_new" RENAME TO "PermissionType";
DROP TYPE "PermissionType_old";
COMMIT;

-- CreateTable
CREATE TABLE "tbl_role_permission_mapping" (
    "role_permission_map_id" UUID NOT NULL,
    "role_id" UUID NOT NULL,
    "permission_id" UUID NOT NULL,
    "type" TEXT DEFAULT 'SYSTEM_DEFINED',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_role_permission_mapping_pkey" PRIMARY KEY ("role_permission_map_id")
);

-- AddForeignKey
ALTER TABLE "tbl_role_permission_mapping" ADD CONSTRAINT "tbl_role_permission_mapping_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "tbl_permission_lk"("permission_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_role_permission_mapping" ADD CONSTRAINT "tbl_role_permission_mapping_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "tbl_role_lk"("role_id") ON DELETE RESTRICT ON UPDATE CASCADE;
