import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateNoteSectionDTO } from './CreateNoteSection.dto';

export class CreateNoteTypeDTO {
  @ApiProperty({
    description: 'Name of the note type',
    example: 'Meeting Notes',
  })
  @IsNotEmpty()
  note_type_name: string;

  @ApiProperty({
    description: 'List of sections associated with the note type',
    type: [CreateNoteSectionDTO],
    example: [
      { section_name: 'Introduction' },
      { section_name: 'Discussion' },
      { section_name: 'Conclusion' },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateNoteSectionDTO)
  sections: CreateNoteSectionDTO[];
}
