import { Module } from '@nestjs/common';
import { UserrolemappingController } from './userrolemapping.controller';
import { UserrolemappingService } from './userrolemapping.service';
import { UserrolemappingRepository } from './userrolemapping.repository';

@Module({
  controllers: [UserrolemappingController],
  providers: [UserrolemappingService,UserrolemappingRepository]
})
export class UserrolemappingModule {}
