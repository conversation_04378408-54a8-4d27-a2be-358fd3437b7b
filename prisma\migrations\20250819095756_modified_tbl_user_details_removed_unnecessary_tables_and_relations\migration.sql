/*
  Warnings:

  - The primary key for the `tbl_user_details` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `user_detail_id` on the `tbl_user_details` table. All the data in the column will be lost.
  - You are about to drop the column `user_detail_id` on the `tbl_user_permission_mapping` table. All the data in the column will be lost.
  - You are about to drop the column `user_detail_id` on the `tbl_user_role_mapping` table. All the data in the column will be lost.
  - You are about to drop the `tbl_identifier_lk` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tbl_object_type_lk` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tbl_object_type_value` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tbl_permission_and_permission_set_mapping` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tbl_permission_set` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tbl_role_permission_set_mapping` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tbl_user_permission_set_mapping` table. If the table is not empty, all the data it contains will be lost.
  - The required column `user_id` was added to the `tbl_user_details` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - Added the required column `user_id` to the `tbl_user_permission_mapping` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `tbl_user_role_mapping` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "tbl_object_type_value" DROP CONSTRAINT "tbl_object_type_value_identifier_id_fkey";

-- DropForeignKey
ALTER TABLE "tbl_object_type_value" DROP CONSTRAINT "tbl_object_type_value_type_name_fkey";

-- DropForeignKey
ALTER TABLE "tbl_permission_and_permission_set_mapping" DROP CONSTRAINT "tbl_permission_and_permission_set_mapping_permission_lk_id_fkey";

-- DropForeignKey
ALTER TABLE "tbl_permission_and_permission_set_mapping" DROP CONSTRAINT "tbl_permission_and_permission_set_mapping_permission_set_i_fkey";

-- DropForeignKey
ALTER TABLE "tbl_permission_lk" DROP CONSTRAINT "tbl_permission_lk_object_type_value_id_fkey";

-- DropForeignKey
ALTER TABLE "tbl_role_permission_set_mapping" DROP CONSTRAINT "tbl_role_permission_set_mapping_perm_set_id_fkey";

-- DropForeignKey
ALTER TABLE "tbl_role_permission_set_mapping" DROP CONSTRAINT "tbl_role_permission_set_mapping_role_lk_id_fkey";

-- DropForeignKey
ALTER TABLE "tbl_user_permission_mapping" DROP CONSTRAINT "tbl_user_permission_mapping_user_detail_id_fkey";

-- DropForeignKey
ALTER TABLE "tbl_user_permission_set_mapping" DROP CONSTRAINT "tbl_user_permission_set_mapping_permission_set_id_fkey";

-- DropForeignKey
ALTER TABLE "tbl_user_permission_set_mapping" DROP CONSTRAINT "tbl_user_permission_set_mapping_user_detail_id_fkey";

-- DropForeignKey
ALTER TABLE "tbl_user_role_mapping" DROP CONSTRAINT "tbl_user_role_mapping_user_detail_id_fkey";

-- DropIndex
DROP INDEX "tbl_user_details_email_key";

-- DropIndex
DROP INDEX "tbl_user_role_mapping_user_detail_id_idx";

-- DropIndex
DROP INDEX "tbl_user_role_mapping_user_detail_id_role_lk_id_key";

-- AlterTable
ALTER TABLE "tbl_user_details" DROP CONSTRAINT "tbl_user_details_pkey",
DROP COLUMN "user_detail_id",
ADD COLUMN     "last_login" TIMESTAMP(3),
ADD COLUMN     "login_id" TEXT,
ADD COLUMN     "password" TEXT,
ADD COLUMN     "profile_picture" TEXT,
ADD COLUMN     "tenant_id" UUID,
ADD COLUMN     "user_id" UUID NOT NULL,
ALTER COLUMN "user_name" DROP NOT NULL,
ADD CONSTRAINT "tbl_user_details_pkey" PRIMARY KEY ("user_id");

-- AlterTable
ALTER TABLE "tbl_user_permission_mapping" DROP COLUMN "user_detail_id",
ADD COLUMN     "user_id" UUID NOT NULL;

-- AlterTable
ALTER TABLE "tbl_user_role_mapping" DROP COLUMN "user_detail_id",
ADD COLUMN     "user_id" UUID NOT NULL;

-- DropTable
DROP TABLE "tbl_identifier_lk";

-- DropTable
DROP TABLE "tbl_object_type_lk";

-- DropTable
DROP TABLE "tbl_object_type_value";

-- DropTable
DROP TABLE "tbl_permission_and_permission_set_mapping";

-- DropTable
DROP TABLE "tbl_permission_set";

-- DropTable
DROP TABLE "tbl_role_permission_set_mapping";

-- DropTable
DROP TABLE "tbl_user_permission_set_mapping";

-- CreateIndex
CREATE INDEX "tbl_user_role_mapping_user_id_idx" ON "tbl_user_role_mapping"("user_id");

-- AddForeignKey
ALTER TABLE "tbl_user_details" ADD CONSTRAINT "tbl_user_details_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenant"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_user_role_mapping" ADD CONSTRAINT "tbl_user_role_mapping_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "tbl_user_details"("user_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_user_permission_mapping" ADD CONSTRAINT "tbl_user_permission_mapping_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "tbl_user_details"("user_id") ON DELETE RESTRICT ON UPDATE CASCADE;
