import { HttpStatus, Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "src/common/prisma/prisma.service";
import { UtilsService } from "src/common/utils/utils.service";
import { CreatePermissionDto } from "./dto/create-permission.dto";
import { tbl_permission_lk } from "@prisma/client";

@Injectable()
export class PermissionsRepository {
    private readonly logger = new Logger(PermissionsRepository.name);
    constructor(private readonly prismaservice: PrismaService,
        private readonly utilsService: UtilsService
    ) { }


    //Create permissions
    async createPermission(data: CreatePermissionDto, userEmail: string): Promise<tbl_permission_lk[]> {
        try {
            const permissions = await this.prismaservice.tbl_permission_lk.createManyAndReturn({
                data: data.info.map((info) => ({
                    key: info.key,
                    description: info.description,
                    type: info.type ?? 'SYSTEM_DEFINED', // fallback if not passed
                    permission_type: info.permission_type,
                    tenant_id: info.tenant_id,
                    isActive: info.isActive ?? true,
                    created_by: userEmail || info.created_by || 'ADMIN',
                })),
            });
            return permissions;
        }
        catch (error) {
            this.logger.error('Error creating permission:', error?.stack);
            throw this.utilsService.formatErrorResponse(
                error,
                'Failed to create permission',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    //Get permissions
    async getPermissions(): Promise<tbl_permission_lk[]> {
        try {
            const permissions = await this.prismaservice.tbl_permission_lk.findMany();
            return permissions;
        } catch (error) {
            this.logger.error('Error fetching permissions:', error?.stack);
            throw this.utilsService.formatErrorResponse(
                error,
                'Failed to fetch permissions',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

}