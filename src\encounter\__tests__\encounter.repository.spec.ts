import { Test, TestingModule } from '@nestjs/testing';
import { EncounterRepository } from '../encounter.repository';
import { PrismaService } from '../../common/prisma/prisma.service';
import { CreateEncounterDTO } from '../dtos/CreateEncounter.dto';

describe('EncounterRepository', () => {
    let repository: EncounterRepository;
    let prismaService: PrismaService;

    const mockPrismaService = {
        encounter: {
            findMany: jest.fn(),
            findUnique: jest.fn(),
        },
        patient: {
            create: jest.fn(),
        },
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                EncounterRepository,
                {
                    provide: PrismaService,
                    useValue: mockPrismaService,
                },
            ],
        }).compile();

        repository = module.get<EncounterRepository>(EncounterRepository);
        prismaService = module.get<PrismaService>(PrismaService);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('insertEncounter', () => {
        it('should create a patient and return encounterId and patientId', async () => {
            const dto: CreateEncounterDTO = {
                patientFirstName: 'John',
                patientLastName: 'Doe',
                patientDOB: '1980-01-01',
                patientGender: 'MALE',
                patientPhone: '',
                phoneCountryCode: '',
            };

            const mockPatient = {
                patient_id: 'pat1',
                encounters: [
                    {
                        encounter_id: 'enc1',
                    },
                ],
            };

            mockPrismaService.patient.create.mockResolvedValue(mockPatient);

            const result = await repository.insertEncounter(dto);
            expect(mockPrismaService.patient.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    first_name: 'John',
                    last_name: 'Doe',
                    date_of_birth: expect.any(String),
                    gender: 'MALE',
                    encounters: { create: { encounter_date: expect.any(Date) } },
                }),
                include: { encounters: true },
            });
            expect(result).toEqual({
                encounterId: 'enc1',
                patientId: 'pat1',
            });
        });

        it('should throw an error if creation fails', async () => {
            mockPrismaService.patient.create.mockRejectedValue(new Error('DB Error'));

            await expect(
                repository.insertEncounter({
                    patientFirstName: 'John',
                    patientLastName: 'Doe',
                    patientDOB: '1980-01-01',
                    patientGender: 'MALE',
                    patientPhone: '',
                    phoneCountryCode: '',
                })
            ).rejects.toThrow('DB Error');
        });
    });

    describe('getEncounterById', () => {
        it('should return transformed encounter if found', async () => {
            const encounterId = 'enc1';
            const mockEncounter = {
                encounter_id: encounterId,
                encounter_date: new Date('2020-01-01'),
                patient: {
                    first_name: 'Jane',
                    last_name: 'Doe',
                    gender: 'FEMALE',
                    date_of_birth: new Date('1990-01-01'),
                    phone_number: null,
                    phone_country_code: null,
                },
            };

            mockPrismaService.encounter.findUnique.mockResolvedValue(mockEncounter);

            const result = await repository.getEncounterById(encounterId);
            expect(mockPrismaService.encounter.findUnique).toHaveBeenCalledWith({
                where: { encounter_id: encounterId },
                select: {
                    encounter_id: true,
                    encounter_date: true,
                    patient: {
                        select: {
                            first_name: true,
                            last_name: true,
                            gender: true,
                            date_of_birth: true,
                            phone_number: true,
                            phone_country_code: true,
                        },
                    },
                },
            });
            expect(result).toEqual({
                encounterId: 'enc1',
                encounterDate: new Date('2020-01-01'),
                patient: {
                    patientFirstName: 'Jane',
                    patientLastName: 'Doe',
                    patientGender: 'FEMALE',
                    patientDOB: new Date('1990-01-01'),
                    patientPhone: '',
                    phoneCountryCode: '',
                },
            });
        });

        it('should return null if encounter not found', async () => {
            mockPrismaService.encounter.findUnique.mockResolvedValue(null);

            const result = await repository.getEncounterById('nonexistent');
            expect(result).toBeNull();
        });
    });

    describe('getAllEncounters', () => {
        it('should return transformed encounters', async () => {
            const mockEncounters = [
                {
                    encounter_id: 'enc1',
                    encounter_date: new Date('2021-01-01'),
                    patient: {
                        first_name: 'John',
                        last_name: 'Doe',
                        gender: 'MALE',
                        date_of_birth: new Date('1980-01-01'),
                        phone_number: '**********',
                        phone_country_code: '1',
                    },
                },
            ];

            mockPrismaService.encounter.findMany.mockResolvedValue(mockEncounters);

            const result = await repository.getAllEncounters(1, 10);
            expect(mockPrismaService.encounter.findMany).toHaveBeenCalledWith({
                skip: 0,
                take: 10,
                select: {
                    encounter_id: true,
                    encounter_date: true,
                    patient: {
                        select: {
                            first_name: true,
                            last_name: true,
                            gender: true,
                            date_of_birth: true,
                            phone_number: true,
                            phone_country_code: true,
                        },
                    },
                },
            });
            expect(result).toEqual([
                {
                    encounterId: 'enc1',
                    encounterDate: new Date('2021-01-01'),
                    patient: {
                        patientFirstName: 'John',
                        patientLastName: 'Doe',
                        patientGender: 'MALE',
                        patientDOB: new Date('1980-01-01'),
                        patientPhone: '**********',
                        phoneCountryCode: '1',
                    },
                },
            ]);
        });

        it('should handle empty results', async () => {
            mockPrismaService.encounter.findMany.mockResolvedValue([]);
            const result = await repository.getAllEncounters(2, 5);
            expect(mockPrismaService.encounter.findMany).toHaveBeenCalledWith({
                skip: 5,
                take: 5,
                select: {
                    encounter_id: true,
                    encounter_date: true,
                    patient: {
                        select: {
                            first_name: true,
                            last_name: true,
                            gender: true,
                            date_of_birth: true,
                            phone_number: true,
                            phone_country_code: true,
                        },
                    },
                },
            });
            expect(result).toEqual([]);
        });
    });
});
