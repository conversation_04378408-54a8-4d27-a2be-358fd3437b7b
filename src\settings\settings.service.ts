import { Injectable } from '@nestjs/common';
import { SettingsRepository } from './settings.repository';

@Injectable()
export class SettingsService {
  constructor(private readonly settingsRepository: SettingsRepository) {}
  async getSettingByConfigType(config_type: string) {
    return await this.settingsRepository.getSettingByConfigType(config_type);
  }

  async updateSetting(setting_key: string, data: any, userEmail: string) {
    return await this.settingsRepository.updateSetting(
      setting_key,
      data,
      userEmail,
    );
  }
}
