/*
  Warnings:

  - The primary key for the `tbl_user_permission_mapping` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `permission_lk_id` on the `tbl_user_permission_mapping` table. All the data in the column will be lost.
  - You are about to drop the column `user_perm_map_id` on the `tbl_user_permission_mapping` table. All the data in the column will be lost.
  - Added the required column `permission_id` to the `tbl_user_permission_mapping` table without a default value. This is not possible if the table is not empty.
  - The required column `user_permission_id` was added to the `tbl_user_permission_mapping` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.

*/
-- DropForeignKey
ALTER TABLE "tbl_user_permission_mapping" DROP CONSTRAINT "tbl_user_permission_mapping_permission_lk_id_fkey";

-- AlterTable
ALTER TABLE "tbl_user_permission_mapping" DROP CONSTRAINT "tbl_user_permission_mapping_pkey",
DROP COLUMN "permission_lk_id",
DROP COLUMN "user_perm_map_id",
ADD COLUMN     "permission_id" UUID NOT NULL,
ADD COLUMN     "type" TEXT DEFAULT 'SYSTEM_DEFINED',
ADD COLUMN     "user_permission_id" UUID NOT NULL,
ADD CONSTRAINT "tbl_user_permission_mapping_pkey" PRIMARY KEY ("user_permission_id");

-- CreateIndex
CREATE INDEX "tbl_user_permission_mapping_user_id_idx" ON "tbl_user_permission_mapping"("user_id");

-- CreateIndex
CREATE INDEX "tbl_user_permission_mapping_permission_id_idx" ON "tbl_user_permission_mapping"("permission_id");

-- AddForeignKey
ALTER TABLE "tbl_user_permission_mapping" ADD CONSTRAINT "tbl_user_permission_mapping_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "tbl_permission_lk"("permission_id") ON DELETE RESTRICT ON UPDATE CASCADE;
