{"roles": [{"role": "Doctor", "permissions": ["patient.read.self", "patient.read.org", "patient.create.self", "patient.update.self", "patient.update.org", "visit.read.self", "visit.create.self", "visit.update.self", "visit.delete.self", "recording.read.self", "recording.create.self", "recording.update.self", "recording.delete.self", "prescription.read.self", "prescription.create.self", "prescription.update.self", "prescription.delete.self", "lab.read.self", "lab.create.self", "lab.update.self", "lab.delete.self", "vitals.read.self", "vitals.create.self", "vitals.update.self", "advice.read.self", "advice.create.self", "advice.update.self", "context.read.self", "context.create.self", "context.update.self", "context.delete.self", "note.view.self", "note.create.self", "note.update.self", "note.delete.self", "note.export.self"]}, {"role": "Staff", "permissions": ["patient.read.org", "patient.create.org", "patient.update.org", "visit.read.org", "visit.create.org", "visit.update.org", "recording.read.org", "recording.create.org", "lab.read.org", "lab.create.org", "lab.update.org", "vitals.read.org", "vitals.create.org", "vitals.update.org", "advice.read.org", "advice.create.org", "context.read.org", "context.create.org", "note.view.org", "note.create.org"]}, {"role": "<PERSON><PERSON>", "permissions": ["patient.read.org", "patient.create.org", "patient.update.org", "patient.delete.org", "visit.read.org", "visit.create.org", "visit.update.org", "visit.delete.org", "recording.read.org", "recording.create.org", "recording.update.org", "recording.delete.org", "prescription.read.org", "prescription.create.org", "prescription.update.org", "prescription.delete.org", "lab.read.org", "lab.create.org", "lab.update.org", "lab.delete.org", "settings.read.org", "settings.update.org", "department.read.org", "department.create.org", "department.update.org", "department.delete.org", "doctor.read.org", "doctor.create.org", "doctor.update.org", "doctor.delete.org", "staff.read.org", "staff.create.org", "staff.update.org", "staff.delete.org", "vitals.read.org", "vitals.create.org", "vitals.update.org", "advice.read.org", "advice.create.org", "advice.update.org", "context.read.org", "context.create.org", "context.update.org", "context.delete.org", "note.view.org", "note.create.org", "note.update.org", "note.delete.org", "note.export.org", "setting.export.org", "subscription.read.org", "subscription.create.org", "subscription.update.org", "subscription.delete.org"]}]}