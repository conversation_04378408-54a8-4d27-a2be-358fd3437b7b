import { Module } from '@nestjs/common';
import { PatientController } from './patient.controller';
import { PatientRepository } from './patient.repository';
import { PatientService } from './patient.service';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';
import { ConfigService } from '@nestjs/config';

@Module({
  imports: [],
  controllers: [PatientController],
  providers: [
    PatientRepository,
    PatientService,
    ConfigService,
    s3FileUploadService,
  ],
  exports: [],
})
export class PatientModule {}
