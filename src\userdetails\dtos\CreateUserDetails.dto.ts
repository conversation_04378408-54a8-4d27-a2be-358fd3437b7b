import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsOptional,
  IsString,
  IsUUID,
  IsBoolean,
  MaxLength,
} from 'class-validator';

export class CreateUserDetailsDto {
  @ApiProperty({ description: 'User email', example: '<EMAIL>' })
  @IsEmail({}, { message: 'Email must be valid' })
  email: string;

  @ApiPropertyOptional({ description: 'Username of the user', example: 'mahesh123' })
  @IsOptional()
  @IsString()
  user_name?: string;

  @ApiPropertyOptional({ description: 'First name', example: 'Mahesh' })
  @IsOptional()
  @IsString()
  first_name?: string;

  @ApiPropertyOptional({ description: 'Last name', example: 'Kumar' })
  @IsOptional()
  @IsString()
  last_name?: string;

  @ApiPropertyOptional({ description: 'Phone number', example: '+************' })
  @IsOptional()
  @MaxLength(20)
  phone_number?: string;

  @ApiPropertyOptional({ description: 'Password', example: 'StrongPass#123' })
  @IsOptional()
  password?: string;

  @ApiPropertyOptional({ description: 'Login ID', example: 'mahesh-login' })
  @IsOptional()
  login_id?: string;

  @ApiPropertyOptional({ description: 'Profile picture URL', example: 'https://example.com/profile.png' })
  @IsOptional()
  profile_picture?: string;

  @ApiPropertyOptional({ description: 'Tenant ID (UUID)' })
  @IsOptional()
  @IsUUID()
  tenant_id?: string;

  @ApiPropertyOptional({ description: 'Is Active user', example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
