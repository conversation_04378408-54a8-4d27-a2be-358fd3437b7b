-- CreateTable
CREATE TABLE "tbl_permission_and_permission_set_mapping" (
    "permission_prim_id" UUID NOT NULL,
    "permission_set_id" UUID NOT NULL,
    "permission_lk_id" UUID NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_permission_and_permission_set_mapping_pkey" PRIMARY KEY ("permission_prim_id")
);

-- AddForeignKey
ALTER TABLE "tbl_role_permission_set_mapping" ADD CONSTRAINT "tbl_role_permission_set_mapping_perm_set_id_fkey" FOREIGN KEY ("perm_set_id") REFERENCES "tbl_permission_set"("perm_set_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_permission_and_permission_set_mapping" ADD CONSTRAINT "tbl_permission_and_permission_set_mapping_permission_set_i_fkey" FOREIGN KEY ("permission_set_id") REFERENCES "tbl_permission_set"("perm_set_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_permission_and_permission_set_mapping" ADD CONSTRAINT "tbl_permission_and_permission_set_mapping_permission_lk_id_fkey" FOREIGN KEY ("permission_lk_id") REFERENCES "tbl_permission_lk"("permission_id") ON DELETE RESTRICT ON UPDATE CASCADE;
