import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiResponseDTO, PaginationResDTO } from '../dtos';
import { apiVersion } from '../constants/common.constants';

@Injectable()
export class ResponseInterceptor<T>
  implements NestInterceptor<T, ApiResponseDTO<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<ApiResponseDTO<T>> {
    const ctx = context.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    return next.handle().pipe(
      map((data) => {
        const apiResponse: ApiResponseDTO<T> = {
          statusCode: response.statusCode,
          message: data.message || 'Success',
          path: request.url,
          data: data.data || data,
          apiVersion: apiVersion,
          timestamp: new Date().toISOString(),
        };

        if (data.pagination) {
          apiResponse.pagination = {
            currentPage: data.pagination.currentPage,
            recordsPerPage: data.pagination.recordsPerPage,
            totalRecords: data.pagination.totalRecords,
            totalPages: data.pagination.totalPages,
          } as PaginationResDTO;
        }

        return apiResponse;
      }),
    );
  }
}
