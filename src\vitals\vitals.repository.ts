import { HttpStatus, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';
import { CreateVitalDto, UpdateVitalDto } from './dto/vitals.dto';
import { tbl_vital_measurements } from '@prisma/client';

@Injectable()
export class VitalRepository {
  private readonly logger = new Logger(VitalRepository.name);
  constructor(
    private readonly prismaService: PrismaService,
    private readonly utilService: UtilsService,
  ) { }

  async getVitalByEncounterId(encounterId: string): Promise<tbl_vital_measurements[]> {
    try {
      const vitals = await this.prismaService.tbl_vital_measurements.findMany({
        where: {
          encounter_id: encounterId,
          isActive: true,
        },
        include: {
          tbl_vital_types: true
        }
      });
      return vitals;
    } catch (error) {
      this.logger.error('Error fetching vitals', error);
      throw this.utilService.formatErrorResponse(
        error,
        'Failed to fetch vitals',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createVitals(data: CreateVitalDto, userEmail: string): Promise<tbl_vital_measurements[]> {
    try {
      const vitals = await this.prismaService.tbl_vital_measurements.createManyAndReturn({
        data: data.info.map((info) => ({
          vital_type_id: info.vital_type_id,
          encounter_id: info.encounter_id,
          vital_value: info.vital_value,
          measurement_time: info.measurement_time,
          created_by: userEmail,

        })),
      });

      return vitals;
    } catch (error) {
      this.logger.error('Error creating vitals:', error?.stack);
      throw this.utilService.formatErrorResponse(
        error,
        'Failed to create vitals',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllVitals() {
    return await this.prismaService.tbl_vital_types.findMany();
  }

  async updateVital(
    vital_measurement_id: string,
    data: UpdateVitalDto,
    userEmail: string,
  ): Promise<tbl_vital_measurements> {
    try {
      const existingVital = await this.prismaService.tbl_vital_measurements.findFirst({
        where: {
          vital_measurement_id: vital_measurement_id,
          isActive: true,
        },
        select: {
          vital_measurement_id: true,
        },
      });

      if (!existingVital) {
        this.logger.error(`Vital not found for the given vital ID: ${vital_measurement_id}`);
        throw new NotFoundException('Vital not found');
      }

      const vital = await this.prismaService.tbl_vital_measurements.update({
        where: {
          vital_measurement_id: vital_measurement_id,
          isActive: true,
        },
        data: {
          vital_value: data.vital_value,
          updated_at: new Date(),
          updated_by: userEmail,
        },
      });
      return vital;
    } catch (error) {
      this.logger.error(`Error updating vital for vital id: ${vital_measurement_id}`, error?.stack);
      throw this.utilService.formatErrorResponse(
        error,
        'Failed to update vital',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
