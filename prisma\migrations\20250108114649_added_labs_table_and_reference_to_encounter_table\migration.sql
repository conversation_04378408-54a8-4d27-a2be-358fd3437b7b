-- CreateTable
CREATE TABLE "labs" (
    "lab_id" UUID NOT NULL,
    "lab_name" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "notes" TEXT,
    "reason" TEXT,
    "encounter_id" UUID,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "labs_pkey" PRIMARY KEY ("lab_id")
);

-- CreateIndex
CREATE INDEX "idx_labs_encounter" ON "labs"("encounter_id");

-- AddForeignKey
ALTER TABLE "labs" ADD CONSTRAINT "labs_encounter_id_fkey" FOREIGN KEY ("encounter_id") REFERENCES "encounter"("encounter_id") ON DELETE SET NULL ON UPDATE CASCADE;
