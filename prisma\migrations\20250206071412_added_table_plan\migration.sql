-- CreateTable
CREATE TABLE "plan" (
    "planId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "effectiveDateFrom" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "effectiveDateTo" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "plan_pkey" PRIMARY KEY ("planId")
);
