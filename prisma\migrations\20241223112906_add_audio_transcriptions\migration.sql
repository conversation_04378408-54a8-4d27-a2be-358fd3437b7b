-- CreateTable
CREATE TABLE "audio_transcription" (
    "id" UUID NOT NULL,
    "original_filename" VARCHAR(255) NOT NULL,
    "s3_uri" VARCHAR(512) NOT NULL,
    "file_size" BIGINT NOT NULL,
    "mime_type" VARCHAR(100) NOT NULL,
    "transcription" TEXT,
    "detected_language" VARCHAR(10),
    "patient_id" UUID,
    "encounter_id" UUID,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by" VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    "is_processed" BOOLEAN NOT NULL DEFAULT false,
    "processing_error" TEXT,

    CONSTRAINT "audio_transcription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_AudioTranscriptionToPatient" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_AudioTranscriptionToPatient_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "idx_audio_transcription_patient" ON "audio_transcription"("patient_id");

-- CreateIndex
CREATE INDEX "idx_audio_transcription_encounter" ON "audio_transcription"("encounter_id");

-- CreateIndex
CREATE INDEX "_AudioTranscriptionToPatient_B_index" ON "_AudioTranscriptionToPatient"("B");

-- AddForeignKey
ALTER TABLE "audio_transcription" ADD CONSTRAINT "audio_transcription_encounter_id_fkey" FOREIGN KEY ("encounter_id") REFERENCES "encounter"("encounter_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AudioTranscriptionToPatient" ADD CONSTRAINT "_AudioTranscriptionToPatient_A_fkey" FOREIGN KEY ("A") REFERENCES "audio_transcription"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AudioTranscriptionToPatient" ADD CONSTRAINT "_AudioTranscriptionToPatient_B_fkey" FOREIGN KEY ("B") REFERENCES "patient"("patient_id") ON DELETE CASCADE ON UPDATE CASCADE;
