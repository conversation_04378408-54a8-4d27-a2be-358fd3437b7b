import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../app.module'; // Adjust the path as necessary
import { PrismaService } from '../../common/prisma/prisma.service';
import { Gender } from '@prisma/client';

describe('EncounterController (Integration)', () => {
    let app: INestApplication;
    let prisma: PrismaService;

    beforeAll(async () => {
        const moduleFixture: TestingModule = await Test.createTestingModule({
            imports: [AppModule], // Ensure AppModule imports PrismaModule and EncounterModule
        }).compile();

        app = moduleFixture.createNestApplication();

        // Apply global pipes if used in your main.ts
        app.useGlobalPipes(new ValidationPipe());

        await app.init();

        prisma = app.get<PrismaService>(PrismaService);

        // Clean up the database before running tests
        await prisma.encounter.deleteMany();
        await prisma.patient.deleteMany();
    });

    afterAll(async () => {
        // Clean up after tests
        await prisma.encounter.deleteMany();
        await prisma.patient.deleteMany();
        await prisma.$disconnect();
        await app.close();
    });

    describe('POST /encounter', () => {
        beforeEach(async () => {
            // Clean up before each POST test
            await prisma.encounter.deleteMany();
            await prisma.patient.deleteMany();
        });

        it('should create a new encounter successfully', async () => {
            const createEncounterDto = {
                patientFirstName: 'John',
                patientLastName: 'Doe',
                patientDOB: '1980-01-01',
                patientGender: 'MALE', // Must match the Gender enum
                patientPhone: '+************', // Valid Indian phone number
                phoneCountryCode: '+91',        // Matching country code for India
            };

            const response = await request(app.getHttpServer())
                .post('/encounter')
                .send(createEncounterDto)
                .expect(201);

            expect(response.body).toHaveProperty('statusCode', 201);
            expect(response.body).toHaveProperty('message', 'Encounter Created Successfully!');
            expect(response.body.data).toHaveProperty('encounterId');
            expect(response.body.data).toHaveProperty('patientId');

            // Verify the data in the database
            const { encounterId, patientId } = response.body.data;
            const patient = await prisma.patient.findUnique({
                where: { patient_id: patientId },
            });
            expect(patient).toBeTruthy();
            expect(patient.first_name).toBe(createEncounterDto.patientFirstName);
            expect(patient.last_name).toBe(createEncounterDto.patientLastName);
            expect(patient.gender).toBe(Gender.MALE);
            expect(patient.phone_number).toBe(createEncounterDto.patientPhone);
            expect(patient.phone_country_code).toBe(createEncounterDto.phoneCountryCode);

            const encounter = await prisma.encounter.findUnique({
                where: { encounter_id: encounterId },
            });
            expect(encounter).toBeTruthy();
            expect(encounter.patient_id).toBe(patientId);
        });

        it('should return 400 for invalid data', async () => {
            const invalidDto = {
                patientFirstName: '', // Empty first name
                patientLastName: 'Doe123', // Invalid last name with numbers
                patientDOB: 'invalid-date',
                patientGender: 'INVALID_GENDER',
                patientPhone: 'not-a-phone',
                phoneCountryCode: '123456', // Exceeds max length
            };

            const response = await request(app.getHttpServer())
                .post('/encounter')
                .send(invalidDto)
                .expect(400);

            expect(response.body).toHaveProperty('message');
            expect(response.body.message).toContain('patientFirstName should not be empty');
            expect(response.body.message).toContain('patientLastName must contain only letters (a-zA-Z)');
            expect(response.body.message).toContain('patientDOB must be a valid ISO 8601 date string');
            expect(response.body.message).toContain('patientGender must be a valid enum value');
            expect(response.body.message).toContain('patientPhone must be a valid phone number');
            expect(response.body.message).toContain('phoneCountryCode must be shorter than or equal to 5 characters');
        });
    });

    describe('GET /encounter/:encounterId', () => {
        let testEncounterId: string;
        let testPatientId: string;

        beforeAll(async () => {
            // Create a patient and encounter for testing
            const patient = await prisma.patient.create({
                data: {
                    first_name: 'Jane',
                    last_name: 'Smith',
                    date_of_birth: new Date('1990-05-15'),
                    gender: Gender.FEMALE,
                    phone_number: '+19876543210',
                    phone_country_code: '+1',
                    encounters: {
                        create: {
                            encounter_date: new Date('2023-01-01'),
                        },
                    },
                },
                include: { encounters: true },
            });

            testPatientId = patient.patient_id;
            testEncounterId = patient.encounters[0].encounter_id;
        });

        it('should retrieve an existing encounter by ID', async () => {
            const response = await request(app.getHttpServer())
                .get(`/encounter/${testEncounterId}`)
                .expect(200);

            expect(response.body).toHaveProperty('statusCode', 200);
            expect(response.body).toHaveProperty('message', 'Encounter Details fetched successfully');
            expect(response.body.data).toHaveProperty('encounterId', testEncounterId);
            expect(response.body.data).toHaveProperty('encounterDate');
            expect(response.body.data.patient).toHaveProperty('patientFirstName', 'Jane');
            expect(response.body.data.patient).toHaveProperty('patientLastName', 'Smith');
            expect(response.body.data.patient).toHaveProperty('patientGender', 'FEMALE');
            expect(new Date(response.body.data.patient.patientDOB)).toEqual(new Date('1990-05-15'));
            expect(response.body.data.patient).toHaveProperty('patientPhone', '+19876543210');
            expect(response.body.data.patient).toHaveProperty('phoneCountryCode', '+1');
        });

        it('should return 200 with null data if encounter not found', async () => {
            const nonExistentId = '00000000-0000-0000-0000-000000000000'; // Assuming UUID

            const response = await request(app.getHttpServer())
                .get(`/encounter/${nonExistentId}`)
                .expect(200);

            expect(response.body).toHaveProperty('statusCode', 200);
            expect(response.body).toHaveProperty('message', 'Encounter Details fetched successfully');
            expect(response.body.data).toBeNull();
        });

        it('should return 400 for invalid UUID format', async () => {
            const invalidId = 'invalid-uuid';

            const response = await request(app.getHttpServer())
                .get(`/encounter/${invalidId}`)
                .expect(400);

            expect(response.body).toHaveProperty('message');
            expect(response.body.message).toContain('Validation failed (uuid is expected)');
        });
    });

    describe('GET /encounter', () => {
        beforeEach(async () => {
            // Clean up before each GET /encounter test
            await prisma.encounter.deleteMany();
            await prisma.patient.deleteMany();

            // Seed the database with multiple encounters for pagination tests
            for (let i = 1; i <= 15; i++) {
                await prisma.patient.create({
                    data: {
                        first_name: `Patient${i}`,
                        last_name: 'Test',
                        date_of_birth: new Date(`199${i % 10}-01-01`),
                        gender: Gender.OTHER,
                        phone_number: `+**********${i}`,
                        phone_country_code: '+1',
                        encounters: {
                            create: {
                                encounter_date: new Date(`2023-01-${i < 10 ? '0' + i : i}`),
                            },
                        },
                    },
                });
            }
        });

        it('should retrieve the first page with default limit (1, 10)', async () => {
            const response = await request(app.getHttpServer())
                .get('/encounter')
                .expect(200);

            expect(response.body).toHaveProperty('statusCode', 200);
            expect(response.body).toHaveProperty('message', 'Encounters fetched successfully');
            expect(response.body.data).toHaveLength(10);

            // Verify the order by encounter_date ascending
            for (let i = 0; i < 10; i++) {
                expect(response.body.data[i].patient).toHaveProperty('patientFirstName', `Patient${i + 1}`);
            }
        });

        it('should retrieve the second page with limit 5', async () => {
            const response = await request(app.getHttpServer())
                .get('/encounter?page=2&limit=5')
                .expect(200);

            expect(response.body).toHaveProperty('statusCode', 200);
            expect(response.body).toHaveProperty('message', 'Encounters fetched successfully');
            expect(response.body.data).toHaveLength(5);

            // Verify that the correct encounters are returned
            for (let i = 0; i < 5; i++) {
                expect(response.body.data[i].patient).toHaveProperty('patientFirstName', `Patient${6 + i}`);
            }
        });

        it('should retrieve an empty array if page exceeds total pages', async () => {
            const response = await request(app.getHttpServer())
                .get('/encounter?page=10&limit=5')
                .expect(200);

            expect(response.body).toHaveProperty('statusCode', 200);
            expect(response.body).toHaveProperty('message', 'Encounters fetched successfully');
            expect(response.body.data).toHaveLength(0);
        });

        it('should return 400 for invalid query parameters', async () => {
            const response = await request(app.getHttpServer())
                .get('/encounter?page=invalid&limit=-5')
                .expect(400);

            expect(response.body).toHaveProperty('message');
            expect(response.body.message).toContain('page must be a positive integer number');
            expect(response.body.message).toContain('limit must be a positive integer number');
        });
    });
});
