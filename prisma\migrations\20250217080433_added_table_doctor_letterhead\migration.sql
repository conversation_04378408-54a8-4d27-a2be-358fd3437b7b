-- CreateTable
CREATE TABLE "doctor_letterhead" (
    "letterhead_id" UUID NOT NULL,
    "page_size" TEXT,
    "paged_height" DOUBLE PRECISION,
    "page_width" DOUBLE PRECISION,
    "top_padding" DOUBLE PRECISION,
    "top_padding_px" INTEGER,
    "bottom_padding" DOUBLE PRECISION,
    "bottom_padding_px" INTEGER,
    "units" TEXT,
    "type" TEXT,
    "doctor_id" UUID,
    "is_default" BOOLEAN,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "doctor_letterhead_pkey" PRIMARY KEY ("letterhead_id")
);

-- AddF<PERSON>ignKey
ALTER TABLE "doctor_letterhead" ADD CONSTRAINT "doctor_letterhead_doctor_id_fkey" FOREI<PERSON><PERSON> KEY ("doctor_id") REFERENCES "doctor"("doctor_id") ON DELETE SET NULL ON UPDATE CASCADE;
