-- AlterTable
ALTER TABLE "encounter" 
ADD COLUMN IF NOT EXISTS "note_header" TEXT;

-- CreateTable
CREATE TABLE "tbl_identifier_lk" (
    "identifier_id" UUID NOT NULL,
    "module_name" TEXT,
    "fe_identifier" JSO<PERSON>,
    "be_identifier" JSON,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_identifier_lk_pkey" PRIMARY KEY ("identifier_id")
);