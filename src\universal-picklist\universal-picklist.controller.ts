import { Body, Controller, Get, Post } from '@nestjs/common';
import { UniversalPicklistService } from './universal-picklist.service';

@Controller('universal-picklist')
export class UniversalPicklistController {
  constructor(
    private readonly universalPicklistService: UniversalPicklistService,
  ) {}

  @Post('universal-picklist-data')
  async GetUniversalPicklistData(
    @Body()
    {
      master_code,
      parent_record_id,
      domain,
    }: {
      master_code: string;
      parent_record_id: string[];
      domain?: string;
    },
  ): Promise<any> {
    return await this.universalPicklistService.GetUniversalPicklistData({
      master_code,
      parent_record_id,
      domain,
    });
  }
}
