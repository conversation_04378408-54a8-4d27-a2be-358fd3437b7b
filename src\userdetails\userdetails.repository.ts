import { Injectable, Logger, NotFoundException, HttpStatus } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';
import { tbl_user_details } from '@prisma/client';  // Prisma generates this type
import { CreateUserDetailsDto } from './dtos/CreateUserDetails.dto';
import {
  PaginatedResultDTO,
} from 'src/common/dtos';
@Injectable()
export class UserdetailsRepository {
  private readonly logger = new Logger(UserdetailsRepository.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async createUser(data: CreateUserDetailsDto, userEmail: string): Promise<tbl_user_details> {
    try {
      return await this.prisma.tbl_user_details.create({
        data: {
          user_name: data.user_name,
          first_name: data.first_name,
          last_name: data.last_name,
          email: data.email,
          phone_number: data.phone_number,
          password: data.password,
          login_id: data.login_id,
          profile_picture: data.profile_picture,
          created_by: userEmail,
        },
      });
    } catch (error) {
      this.logger.error('Error creating user:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  

  async findUsers(email?: string, user_name?: string, userEmail?: string, page = 1, limit = 10)  : Promise<PaginatedResultDTO<tbl_user_details>> {
   const where: any = {
  isActive: true,
  OR: [],
};

if (email) {
  where.OR.push({ email: { contains: email, mode: 'insensitive' } });
}
if (user_name) {
  where.OR.push({ user_name: { contains: user_name, mode: 'insensitive' } });
}
if (where.OR.length === 0) delete where.OR;

    const [items, total] = await this.prisma.$transaction([
      this.prisma.tbl_user_details.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { created_at: 'desc' },
      }),
      this.prisma.tbl_user_details.count({ where }),
    ]);

   return {
  data: items,
  pagination: this.utilsService.getPagination(page, limit, total),
};
  }

  async getUserById(user_id: string): Promise<tbl_user_details> {
    try {
      const user = await this.prisma.tbl_user_details.findUnique({
        where: { user_id },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return user;
    } catch (error) {
      this.logger.error('Error fetching user by ID:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch user by ID',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateUser(user_id: string, data: Partial<CreateUserDetailsDto>, userEmail: string): Promise<tbl_user_details> {
    try {
      return await this.prisma.tbl_user_details.update({
        where: { user_id },
        data: {
          ...data,
          updated_by: userEmail,
          updated_at: new Date(),
        },
      });
    } catch (error) {
      this.logger.error('Error updating user:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteUser(user_id: string, userEmail: string): Promise<tbl_user_details> {
    try {
      return await this.prisma.tbl_user_details.update({
        where: { user_id },
        data: {
          isActive: false,
          updated_by: userEmail,
          updated_at: new Date(),
        },
      });
    } catch (error) {
      this.logger.error('Error deleting user:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
