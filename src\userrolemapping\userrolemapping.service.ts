import { Injectable, Logger, HttpStatus } from '@nestjs/common';
import { tbl_user_role_mapping } from '@prisma/client';
import { UserrolemappingRepository } from './userrolemapping.repository';
import { CreateUserRoleMappingDto } from './dtos/dtos/create-user-role-mapping.dto';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class UserrolemappingService {
  private readonly logger = new Logger(UserrolemappingService.name);

  constructor(
    private readonly repo: UserrolemappingRepository,
    private readonly utilsService: UtilsService,
  ) {}

  async create(
    dto: CreateUserRoleMappingDto,
    userEmail: string,
  ): Promise<tbl_user_role_mapping> {
    try {
      return await this.repo.create(dto, userEmail);
    } catch (error) {
      this.logger.error('Error in service create:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: string): Promise<tbl_user_role_mapping> {
    try {
      return await this.repo.findOne(id);
    } catch (error) {
      this.logger.error('Error in service findOne:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch mapping',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async findAll(): Promise<tbl_user_role_mapping[]> {
    try {
      return await this.repo.findAll();
    } catch (error) {
      this.logger.error('Error in service findAll:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch all mappings',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async update(
    id: string,
    dto: Partial<CreateUserRoleMappingDto>,
    userEmail: string,
  ): Promise<tbl_user_role_mapping> {
    try {
      return await this.repo.update(id, dto, userEmail);
    } catch (error) {
      this.logger.error('Error in service update:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async remove(id: string, userEmail: string): Promise<tbl_user_role_mapping> {
    try {
      return await this.repo.remove(id, userEmail);
    } catch (error) {
      this.logger.error('Error in service remove:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
