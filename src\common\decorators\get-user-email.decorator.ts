import {createParamDecorator, ExecutionContext, UnauthorizedException} from '@nestjs/common';

export const GetUserEmail = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): string => {
        const request = ctx.switchToHttp().getRequest();
        const user = request.user;
        if (!user || !user.email) {
            throw new UnauthorizedException('Invalid token or email claim missing')
        }
        return user.email;
    },
);
