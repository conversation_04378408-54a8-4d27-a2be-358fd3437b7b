import { Module } from '@nestjs/common';
import { FeedbackService } from './feedback.service';
import { FeedbackController } from './feedback.controller';
import { FeedbackRepository } from './feedback.repository';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';
import { ConfigService } from '@nestjs/config';

@Module({
  controllers: [FeedbackController],
  providers: [FeedbackService, FeedbackRepository,ConfigService,s3FileUploadService],
})
export class FeedbackModule {}
