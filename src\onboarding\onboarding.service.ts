import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { PlanRepository } from 'src/plan/plan.repository';
import { SubscriptionRepository } from 'src/subscription/subscription.repository';
import { OnboardingRepository } from './onboarding.repository';
import { DoctorRepository } from 'src/doctor/doctor.repository';
import { VERIFICATION_STATUS } from 'src/common/constants/common.constants';
import { Doctor } from '@prisma/client';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class OnboardingService {
  private readonly logger = new Logger(OnboardingService.name);
  constructor(
    private readonly onboardingRepository: OnboardingRepository,
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly planRepository: PlanRepository,
    private readonly doctorRepository: DoctorRepository,
    private readonly prismaservice: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async verifyDoctorOnboarding(doctorEmail: string): Promise<{
    verification_status: string;
    doctor_details?: Doctor | any;
  }> {
    try {
      const adminCount = await this.prismaservice.admin.count({
        where: {
          isActive: true,
          email: doctorEmail,
        },
      });

      const doctorDetails: any =
        await this.doctorRepository.getDoctorByEmail(doctorEmail);


        if (!doctorDetails) {
          return {
            verification_status: VERIFICATION_STATUS.INCOMPLETE_PROFILE,
            doctor_details: doctorDetails ?? { email: doctorEmail },
          };
        }
        if (!doctorDetails?.isVerified) {
          return {
            verification_status: VERIFICATION_STATUS.INCOMPLETE_PROFILE,
            doctor_details: doctorDetails,
          };
        }
      if (
        !doctorDetails?.TenantDoctor ||
        doctorDetails?.TenantDoctor?.length == 0
      ) {
        return {
          verification_status: VERIFICATION_STATUS.NO_ACTIVE_TENANT,
          doctor_details: doctorDetails ?? { email: doctorEmail },
        };
      }
      if (adminCount > 0) {
        if (doctorDetails) {
          return {
            verification_status: VERIFICATION_STATUS.ADMIN,
            doctor_details: doctorDetails ?? { email: doctorEmail },
          };
        } else {
          return {
            verification_status: VERIFICATION_STATUS.INCOMPLETE_PROFILE,
            doctor_details: doctorDetails ?? { email: doctorEmail },
          };
        }
      }

     

      if (doctorDetails?.isVerified) {
        //check if doctor has a subscription
        // const subscriptionDetails =
        //   await this.subscriptionRepository.getSubscriptionByDoctorEmail(
        //     doctorDetails.email,
        //   );

        //need to remove this section later
        const subscriptionDetails = {
          subscriptionId: '',
          planId: '2b49a4ec-1a6a-47cc-b374-5bcfbd7f343a',
          isActive: true,
          status: 'ACTIVE',
          effectiveDateFrom: new Date(),
          effectiveDateTo: new Date(
            new Date().setMonth(new Date().getMonth() + 1),
          ),
        };
        if (!subscriptionDetails) {
          return {
            verification_status: VERIFICATION_STATUS.NO_SUBSCRIPTION,
            doctor_details: doctorDetails ?? { email: doctorEmail },
          };
        }

        if (subscriptionDetails?.status == 'EXPIRED') {
          return {
            verification_status: VERIFICATION_STATUS.SUBSCRIPTION_EXPIRED,
            doctor_details: doctorDetails ?? { email: doctorEmail },
          };
        }

        if (!subscriptionDetails?.effectiveDateTo) {
          return {
            verification_status: VERIFICATION_STATUS.NO_SUBSCRIPTION,
            doctor_details: doctorDetails ?? { email: doctorEmail },
          };
        }

        if (subscriptionDetails?.effectiveDateTo < new Date()) {
          //if subscription expired
          const updatedSubscription =
            await this.subscriptionRepository.updateSubscriptionStatusByIdAndUserId(
              subscriptionDetails.subscriptionId,
              doctorEmail,
              'EXPIRED',
            );

          return {
            verification_status: VERIFICATION_STATUS.SUBSCRIPTION_EXPIRED,
            doctor_details: doctorDetails ?? { email: doctorEmail },
          };
        } else if (subscriptionDetails?.effectiveDateTo > new Date()) {
          return {
            verification_status: VERIFICATION_STATUS.SUBSCRIPTION_ACTIVE,
            doctor_details: doctorDetails ?? { email: doctorEmail },
          };
        }
      }
    } catch (error) {
      this.logger.error(`Error verifying doctor onboarding doctorEmail: ${doctorEmail}`, error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to verify doctor onboarding',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
