import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './doctor.controller';
import { DoctorRepository } from './doctor.repository';
import { DoctorService } from './doctor.service';

@Module({
  controllers: [DoctorController],
  providers: [Doctor<PERSON><PERSON><PERSON><PERSON>y, DoctorService],
  exports: [DoctorR<PERSON>ository, DoctorService],
})
export class DoctorModule {}
