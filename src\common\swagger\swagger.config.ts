import { INestApplication, Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export function setupSwagger(app: INestApplication) {
  const logger = new Logger('SwaggerSetup');

  const config = new DocumentBuilder()
    .setTitle('Medical Encounters API')
    .setDescription('API for managing medical encounters and doctors')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter your JWT token in the format: Bearer <token>',
        in: 'header',
      },
      'access-token', // This name should match the one used in @ApiBearerAuth()
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  logger.log('Swagger UI is available at /api');
}
