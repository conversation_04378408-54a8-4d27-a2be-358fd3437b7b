import { Controller, Get } from '@nestjs/common';
import { AccessControlService } from './access-control.service';
import { GetUserEmail } from '../decorators/get-user-email.decorator';

@Controller('access-control')
export class AccessControlController {
  constructor(private readonly accessControlService: AccessControlService) {}

  @Get('get-permissions')
  async getUserPermissions(@GetUserEmail() userEmail: string) {
    return await this.accessControlService.getUserPermissions(userEmail);
  }
}
