import { Injectable, Logger, HttpStatus, Global } from '@nestjs/common';
import {
  DeleteObjectCommand,
  GetObjectAclCommandOutput,
  GetObjectCommand,
  GetObjectCommandOutput,
  S3,
} from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import { ConfigService } from '@nestjs/config';
import { NodeHttpHandler } from '@smithy/node-http-handler';
import { UtilsService } from '../utils/utils.service';

@Global()
@Injectable()
export class s3FileUploadService {
  private readonly logger = new Logger(s3FileUploadService.name);
  private s3: S3;
  private s3Bucket: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly utilsService: UtilsService
  ) {
    this.s3 = new S3({
      credentials: {
        accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      },
      region: this.configService.get('AWS_REGION'),
      requestHandler: new NodeHttpHandler({
        connectionTimeout: 20000,
        socketTimeout: 60000,
      }),
    });

    this.s3Bucket = this.configService.get('AWS_S3_BUCKET_NAME');
  }

  async uploadFileToS3(
    file: Express.Multer.File,
    filePath: string,
  ): Promise<string> {
    try {
      this.logger.log(`Uploading file to S3: ${filePath}`);

      const multipartUpload = new Upload({
        client: this.s3,
        params: {
          Bucket: this.s3Bucket,
          Key: filePath,
          Body: file.buffer,
          ContentType: file.mimetype,
        },
      });

      multipartUpload.on('httpUploadProgress', (progress) => {
        this.logger.log(
          `Upload progress: ${progress.loaded} / ${progress.total} bytes`,
        );
      });

      const s3Response = await multipartUpload.done();

      this.logger.log(`File uploaded successfully: ${s3Response.Location}`);
      return s3Response.Location;
    } catch (error) {
      this.logger.error(
        `Error uploading file to S3: ${error.message}`,
        error?.stack,
      );
      throw error;
    }
  }

  async deleteFileFromS3(fileLocation: string): Promise<boolean> {
    try {
      this.logger.log(`Deleting file from S3: ${fileLocation}`);

      // Extract the key from the file location URL
      // Assuming the fileLocation is in the format: https://bucket-name.s3.region.amazonaws.com/path/to/file
      // We need to extract the part after the bucket name: 'path/to/file'
      const { bucket, key } = this.ParseS3Url(fileLocation);

      const command = new DeleteObjectCommand({
        Bucket: this.s3Bucket,
        Key: key,
      });

      await this.s3.send(command);

      this.logger.log(`File deleted successfully: ${fileLocation}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Error deleting file from S3: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  async downloadFileFromS3(fileLocation: string): Promise<any> {
    try {
      this.logger.log(`Downloading file from S3: ${fileLocation}`);

      const { bucket, key } = this.ParseS3Url(fileLocation);

      const command = new GetObjectCommand({
        Bucket: bucket,
        Key: key,
      });

      const s3Response: GetObjectCommandOutput = await this.s3.send(command);

      // Convert the readable stream to a buffer
      const chunks = [];
      for await (const chunk of s3Response.Body as any) {
        chunks.push(chunk);
      }
      const fileBuffer = Buffer.concat(chunks);

      this.logger.log(
        `File downloaded successfully: ${fileLocation} (${fileBuffer.length} bytes)`,
      );
      return {
        ContentLength: s3Response.ContentLength,
        ContentType: s3Response.ContentType,
        Buffer: fileBuffer,
      };
    } catch (error) {
      this.logger.error(
        `Error downloading file from S3: ${error.message}`,
        error.stack,
      );
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to download file from storage',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  private ParseS3Url(url: string): { bucket: string; key: string } {
    const parsedUrl = new URL(url);
    // console.log('parsedUrl :', parsedUrl);
    const bucket = parsedUrl.hostname.split('.')[0];
    // console.log('bucket :', bucket);
    const key = decodeURIComponent(parsedUrl.pathname.slice(1));
    // console.log('key :', key);

    return { bucket, key };
  }
}
