import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';

export class CreatePrescriptionDto {
  @Type(() => PrescriptionDetailsDto)
  @ValidateNested({ each: true })
  info: PrescriptionDetailsDto[];
}

export class PrescriptionDetailsDto {
  @ApiProperty({
    description: 'The name of the lab test',
    example: ' 6fd6be04-e7e3-448b-8dc3-dbc6636563a3',
  })
  @IsNotEmpty({ message: 'Encounter id is Required' })
  @IsString({ message: 'Encounter id must be a string' })
  @IsUUID()
  encounter_id: string;

  @ApiProperty({
    description: 'The name of the drug test',
    example: 'Metamicine',
  })
  @IsNotEmpty({ message: 'Drug name is Required' })
  drug_name: string;

  @ApiProperty({
    description: 'The dose for the drug ',
    example: 'this is a dose',
  })
  @IsOptional()
  dose: string;

  @ApiProperty({
    description: 'The sig',
    example: 'this is a sig',
  })
  @IsOptional()
  sig: string;
  @ApiProperty({
    description: 'The brand',
    example: 'this is a brand',
  })
  @IsOptional()
  brand: string;
  @ApiProperty({
    description: 'The notes of patients',
    example: 'this is a note from patient',
  })
  @IsOptional()
  patient_notes: string;

  @ApiProperty({
    description: 'status of the Prscription',
    example: 'PENDING | ACCEPTED | REJECTED',
  })
  @IsNotEmpty()
  @IsString()
  status: string;

  @ApiProperty({
    description: 'created by',
    example: '',
  })
  @IsOptional()
  created_by: string;
}

export class UpdatePrescriptionDto {
  @ApiProperty({
    description: 'The name of the drug test',
    example: 'Metamicine',
  })
  @IsNotEmpty({ message: 'Drug name is Required' })
  drug_name: string;

  @ApiProperty({
    description: 'The dose for the drug ',
    example: 'this is a dose',
  })
  @IsOptional()
  dose: string;

  @ApiProperty({
    description: 'The sig',
    example: 'this is a sig',
  })
  @IsOptional()
  sig: string;

  @ApiProperty({
    description: 'The brand',
    example: 'this is a brand',
  })
  @IsOptional()
  brand: string;

  @ApiProperty({
    description: 'The notes of patients',
    example: 'this is a note from patient',
  })
  @IsOptional()
  patient_notes: string;

}
