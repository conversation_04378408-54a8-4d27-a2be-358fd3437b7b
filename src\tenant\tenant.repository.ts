import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Tenant } from '@prisma/client';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { CreateTenantDto } from './dto/tenant.dto';
import { UtilsService } from 'src/common/utils/utils.service';  
@Injectable()
export class TenantRepository {
  private readonly logger = new Logger(TenantRepository.name);
  constructor(private readonly prismaservice: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async getAllTenants(): Promise<Tenant[]> {
    try {
      const tenants = await this.prismaservice.tenant.findMany({
        where: {
          isActive: true,
        },
      });

      return tenants;
    } catch (error) {
      this.logger.error('Error in getAllTenants:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to get all tenants',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async createTenant(
    model: CreateTenantDto,
    userEmail: string,
  ): Promise<Tenant> {
    try {
      const newTenant = await this.prismaservice.tenant.create({
        data: {
          name: model.name,
          phone_number: model.phone_number,
          email: userEmail,
          created_by: userEmail,
          TenantDoctor: {
            create: {
              doctor: {
                connect: {
                  email: userEmail,
                },
              },
            },
          },
        },
        include: {
          TenantDoctor: true,
        },
      });
      return newTenant;
    } catch (error) {
      this.logger.error('Error in create tenant:', error?.message);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create tenant',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getDoctorTenants(userEmail: string): Promise<Tenant[]> {
    try {
      const tenants = await this.prismaservice.tenant.findMany({
        where: {
          isActive: true,
          TenantDoctor: {
            some: {
              isActive: true,
              doctor: {
                email: userEmail,
              },
            },
          },
        },
      });

      return tenants;
    } catch (error) {
      this.logger.error('Error in get doctor Tenants:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to get doctor tenants',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
