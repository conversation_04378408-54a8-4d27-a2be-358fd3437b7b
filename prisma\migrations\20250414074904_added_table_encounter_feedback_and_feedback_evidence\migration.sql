-- CreateTable
CREATE TABLE "encounter_feedback" (
    "id" UUID NOT NULL,
    "encounter_id" UUID,
    "feedback" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "encounter_feedback_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feedback_evidence" (
    "id" UUID NOT NULL,
    "feedbackId" UUID,
    "url" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "feedback_evidence_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "encounter_feedback" ADD CONSTRAINT "encounter_feedback_encounter_id_fkey" FOREIGN KEY ("encounter_id") REFERENCES "encounter"("encounter_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedback_evidence" ADD CONSTRAINT "feedback_evidence_feedbackId_fkey" FOREIGN KEY ("feedbackId") REFERENCES "encounter_feedback"("id") ON DELETE SET NULL ON UPDATE CASCADE;
