const fs = require('fs');
const path = require('path');

/**
 * Permission seed function
 * @param {import('@prisma/client').PrismaClient} prisma
 */
module.exports = async function (prisma) {
  const filePath = path.join(__dirname, 'permissions.json');
  const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

  const permissions = data.permissions;

  const result = await prisma.tbl_permission_lk.createMany({
    data: permissions.map((p) => ({
      key: typeof p === 'string' ? p : p.key,                // handles both formats
      description: typeof p === 'string' ? p : p.description, // use description if provided
      permission_type: p.permission_type ?? undefined,
      type: 'SYSTEM_DEFINED',
      created_by: 'ADMIN',
    })),
    skipDuplicates: true, // avoids inserting existing ones
  });

  console.log(`✅ Seeded ${result.count} permissions.`);
};
