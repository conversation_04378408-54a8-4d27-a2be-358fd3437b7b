-- Migration: Add vital types seeder data

BEGIN;

INSERT INTO tbl_vital_types (
    vital_type_id,
    unit_name,
    max_value,
    min_value,
    measurement_system,
    vital_type_name,
    data_type,
    display_name,
    notes,
    reasoning,
    show_case
) VALUES 
-- Default <PERSON>ls
('8b5a52e1-cbaa-4008-b8f5-19eaab6efe50', '°C', '45', '30', 'metric', 'temperature_celsius', 'decimal', 'Temperature (°C)', 'Normal range: 36.1 – 37.2 °C. Needs to be calculated based on the unit selected.', 'Hyperthermia beyond 44–45 °C is usually unsurvivable.', 'DEFAULT'),
('7aae1446-3c52-4473-b2d4-56c6fea3c141', '°F', '113', '86', 'imperial', 'temperature_fahrenheit', 'decimal', 'Temperature (°F)', 'Normal range: 97–99 °F. Needs to be calculated based on the unit selected.', 'Hyperthermia beyond 44–45 °C (113°F) is usually unsurvivable.', 'DEFAULT'),
('01462732-72f3-4f85-a116-4a31edc296d8', 'bpm', '300', '30', 'universal', 'heart_rate', 'integer', 'Heart Rate (Pulse)', 'Normal range: 60 – 100 bpm.', 'Ventricular tachycardia/fibrillation territory; sustained >250 is lethal.', 'DEFAULT'),
('a95af765-b804-404a-84d1-3778fe1ba959', 'breaths/min', '80', '8', 'universal', 'respiratory_rate', 'integer', 'Respiratory Rate', 'Normal range: 12 – 20 breaths/min.', 'Extreme in severe distress; beyond ~80 is not sustainable.', 'DEFAULT'),
('ad61874d-64a8-43d9-82d6-b9864ed3f4b4', 'mmHg', '300', '60', 'universal', 'blood_pressure_systolic', 'integer', 'Blood Pressure – Systolic', 'Normal range: 90 – 120 mmHg. Systolic BP should always be higher than diastolic.', 'Highest recorded ~370 in hypertensive crisis, but 300 is a safe sanity cap.', 'DEFAULT'),
('16ebad8b-f56d-4121-99a6-7e971c3c6a76', 'mmHg', '200', '40', 'universal', 'blood_pressure_diastolic', 'integer', 'Blood Pressure – Diastolic', 'Normal range: 60 – 80 mmHg. Diastolic BP should always be lower than Systolic.', 'Rare malignant hypertension cases approach this; beyond = data error.', 'DEFAULT'),

-- Additional Vitals (Optional)
('17c27321-bc5b-4fe1-85ec-8e27bf421372', '%', '100', '70', 'universal', 'oxygen_saturation', 'integer', 'Oxygen Saturation (SpO₂)', 'Normal range: 95 – 100%.', 'Physiologically impossible to exceed on standard oximetry.', 'OPTIONAL'),
('57ee28e0-6a50-4445-8177-5d2fbca10901', 'cm', '300', '30', 'metric', 'height_cm', 'decimal', 'Height (cm)', 'Needs to be calculated based on the unit selected.', 'Tallest recorded ~272 cm; 300 is a hard cap.', 'OPTIONAL'),
('a22dcc8d-a7bd-441e-b39e-5bf2bbca2fa1', 'ft/in', '9.84', '0.98', 'imperial', 'height_ft', 'decimal', 'Height (ft/in)', 'Needs to be calculated based on the unit selected.', 'Tallest recorded ~8.9 ft; 9.84 ft (300 cm) is a hard cap.', 'OPTIONAL'),
('0b41ee16-2941-419b-afd9-2e0872d0a60c', 'kg', '700', '1', 'metric', 'weight_kg', 'decimal', 'Weight (kg)', 'Needs to be calculated based on the unit selected.', 'Heaviest recorded ~635 kg; 700 is a sanity cap.', 'OPTIONAL'),
('4d7686fe-44a5-4f3e-9922-36d76041c71d', 'lb', '1543', '2.2', 'imperial', 'weight_lb', 'decimal', 'Weight (lb)', 'Needs to be calculated based on the unit selected.', 'Heaviest recorded ~1400 lb; 1543 lb (700 kg) is a sanity cap.', 'OPTIONAL'),
('12d70a8d-5d80-43c4-a81b-caafbe4fa5d5', 'kg/m²', '200', '10', 'universal', 'bmi', 'decimal', 'BMI', 'Normal range: 18.5 – 24.9 kg/m². Needs to be calculated based on height and weight. Formula: Weight in kg/(Height in cm/100)²', 'Mathematical upper limit given extreme weight/height ratios.', 'OPTIONAL'),
('1614fe6d-02bb-4150-8307-1fac578d13fb', '0-10 scale', '10', '0', 'universal', 'pain_score', 'integer', 'Pain Score', 'Normal: 0 (no pain). 0-10 scale.', 'Scale maximum is fixed.', 'OPTIONAL'),
('90900991-15cf-47e1-90a2-d956d6696b89', 'mg/dL', '2000', '40', 'imperial', 'random_blood_glucose_mg', 'integer', 'Random Blood Glucose (mg/dL)', 'Normal: <140 mg/dL.', 'Extreme hyperglycemia in DKA/HHS; beyond this is almost always an error.', 'OPTIONAL'),
('7bab6bf9-445a-46dd-ae33-8131ecfae6fe', 'mmol/L', '111', '2.2', 'metric', 'random_blood_glucose_mmol', 'decimal', 'Random Blood Glucose (mmol/L)', 'Normal: <7.8 mmol/L.', 'Extreme hyperglycemia in DKA/HHS; beyond this is almost always an error.', 'OPTIONAL'),
('f611699c-07f5-4ad7-bbcc-90b2cb3060af', 'mg/dL', '2000', '40', 'imperial', 'fasting_blood_glucose_mg', 'integer', 'Fasting Blood Glucose (mg/dL)', 'Normal range: 70–99 mg/dL.', 'Same rationale.', 'OPTIONAL'),
('ae7aebf1-9494-4e77-9b92-cc3b3b19618e', 'mmol/L', '111', '2.2', 'metric', 'fasting_blood_glucose_mmol', 'decimal', 'Fasting Blood Glucose (mmol/L)', 'Normal range: 3.9–5.5 mmol/L.', 'Same rationale.', 'OPTIONAL'),
('302ffbfd-1e3e-4d98-9ed7-2be1900dd05d', 'mg/dL', '2000', '40', 'imperial', 'postprandial_blood_glucose_mg', 'integer', 'Postprandial Blood Glucose (mg/dL)', 'Normal: <140 mg/dL.', 'Same rationale.', 'OPTIONAL'),
('f9a258e5-3f00-4617-aa73-585c71fc3e88', 'mmol/L', '111', '2.2', 'metric', 'postprandial_blood_glucose_mmol', 'decimal', 'Postprandial Blood Glucose (mmol/L)', 'Normal: <7.8 mmol/L.', 'Same rationale.', 'OPTIONAL');

COMMIT;