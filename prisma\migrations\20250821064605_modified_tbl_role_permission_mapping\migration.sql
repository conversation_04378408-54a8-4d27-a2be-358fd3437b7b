/*
  Warnings:

  - The primary key for the `tbl_role_permission_mapping` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `role_permission_map_id` on the `tbl_role_permission_mapping` table. All the data in the column will be lost.
  - The required column `role_permission_id` was added to the `tbl_role_permission_mapping` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.

*/
-- AlterTable
ALTER TABLE "tbl_role_permission_mapping" DROP CONSTRAINT "tbl_role_permission_mapping_pkey",
DROP COLUMN "role_permission_map_id",
ADD COLUMN     "role_permission_id" UUID NOT NULL,
ADD CONSTRAINT "tbl_role_permission_mapping_pkey" PRIMARY KEY ("role_permission_id");
