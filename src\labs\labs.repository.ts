import { HttpException, HttpStatus, Injectable, NotFoundException, Logger } from '@nestjs/common';
import { Labs } from '@prisma/client';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { CreateLabDto, LabDetailsDto, UpdateLabDto } from './dtos/labs.dto';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class LabsRepository {
  private readonly logger = new Logger(LabsRepository.name);
  constructor(private readonly prismaservice: PrismaService,
    private readonly utilsService:UtilsService
  ) {}
  async getLabsByEncounterId(encounterId: string): Promise<Labs[]> {
    try {
      const labs = await this.prismaservice.labs.findMany({
        where: {
          encounter_id: encounterId,
          isActive: true,
        },
      });
      return labs;
    } catch (error) {
      this.logger.error('Error fetching labs:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch labs',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async createLab(data: CreateLabDto, userEmail: string): Promise<Labs[]> {
    try {
      const lab = await this.prismaservice.labs.createManyAndReturn({
        data: data.info.map((info) => ({
          lab_name: info.lab_name,
          encounter_id: info.encounter_id,
          reason: info.reason || null,
          notes: info.notes || null,
          created_by:
            info.created_by && info.created_by != ''
              ? info.created_by
              : userEmail,
          status: info.status || null,
        })),
      });
      return lab;
    } catch (error) {
      this.logger.error('Error creating lab:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create lab',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateLab(
    lab_id: string,
    data: UpdateLabDto,
    userEmail: string,
  ): Promise<Labs> {
    try {
      const existingLab = await this.prismaservice.labs.findFirst({
        where: {
          lab_id: lab_id,
          isActive: true,
        },
        select: {
          lab_id: true,
        },
      });

      if (!existingLab) {
        this.logger.error(`Lab not found for the given lab ID: ${lab_id}`);
        throw new NotFoundException('Lab not found');
      }

      const lab = await this.prismaservice.labs.update({
        where: {
          lab_id: lab_id,
          isActive: true,
        },
        data: {
          lab_name: data.lab_name,
          reason: data.reason || null,
          notes: data.notes || null,
          updated_at: new Date(),
          updated_by: userEmail,
        },
      });
      return lab;
    } catch (error) {
      this.logger.error(`Error updating lab for lab id: ${lab_id}`, error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update lab',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async deleteLab(lab_id: string, userEmail: string): Promise<Labs> {
    try {
      const existingLab = await this.prismaservice.labs.findFirst({
        where: {
          lab_id: lab_id,
          isActive: true,
        },
        select: {
          lab_id: true,
        },
      });

      if (!existingLab) {
        this.logger.error(`Lab not found for the given lab ID: ${lab_id}`);
        throw new NotFoundException('Lab not found');
      }

      const lab = await this.prismaservice.labs.update({
        where: {
          lab_id: lab_id,
          isActive: true,
        },
        data: {
          status: 'REJECTED',
          updated_at: new Date(),
          updated_by: userEmail,
        },
      });
      return lab;
    } catch (error) {
      this.logger.error(`Error deleting lab for lab id: ${lab_id}`, error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete lab',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async upadateLabStatusByLabid(
    lab_id: string,
    status_value: string,
    userEmail: string,
    status_reason: string,
  ): Promise<Labs> {
    try {
      const lab = await this.prismaservice.labs.update({
        where: {
          lab_id: lab_id,
          isActive: true,
        },
        data: {
          status: status_value,
          updated_by: userEmail,
          status_reason: status_reason || null,
        },
      });
      return lab;
    } catch (error) {
      this.logger.error(`Error deleting lab: ${lab_id}`, error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update lab status',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
