-- CreateTable
CREATE TABLE "tbl_user_permission_mapping" (
    "user_perm_map_id" UUID NOT NULL,
    "user_detail_id" UUID NOT NULL,
    "permission_lk_id" UUID NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_user_permission_mapping_pkey" PRIMARY KEY ("user_perm_map_id")
);

-- AddForeignKey
ALTER TABLE "tbl_user_permission_mapping" ADD CONSTRAINT "tbl_user_permission_mapping_user_detail_id_fkey" FOREIGN KEY ("user_detail_id") REFERENCES "tbl_user_details"("user_detail_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_user_permission_mapping" ADD CONSTRAINT "tbl_user_permission_mapping_permission_lk_id_fkey" FOREIGN KEY ("permission_lk_id") REFERENCES "tbl_permission_lk"("permission_id") ON DELETE RESTRICT ON UPDATE CASCADE;
