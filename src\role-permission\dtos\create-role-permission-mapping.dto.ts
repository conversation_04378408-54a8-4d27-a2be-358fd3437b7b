// src/rolepermission/dtos/create-role-permission-mapping.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsUUID, IsOptional, IsBoolean, IsString } from 'class-validator';

export class CreateRolePermissionMappingDto {
  @ApiProperty({ description: 'Role ID (UUID)', example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  role_id: string;

  @ApiProperty({ description: 'Permission ID (UUID)', example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  permission_id: string;

  @ApiPropertyOptional({ description: 'Type of mapping', example: 'SYSTEM_DEFINED' })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({ description: 'Is active', example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
