-- CreateTable
CREATE TABLE "note_header" (
    "note_id" UUID NOT NULL,
    "patient_id" UUID NOT NULL,
    "encounter_id" UUID NOT NULL,
    "doctor_id" UUID NOT NULL,
    "note_type_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "note_header_pkey" PRIMARY KEY ("note_id")
);

-- CreateTable
CREATE TABLE "note_detail" (
    "note_detail_id" UUID NOT NULL,
    "note_header_id" UUID NOT NULL,
    "note_section_id" UUID NOT NULL,
    "value" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "note_detail_pkey" PRIMARY KEY ("note_detail_id")
);

-- CreateTable
CREATE TABLE "note_type_lk" (
    "note_type_id" UUID NOT NULL,
    "note_type_name" TEXT NOT NULL,

    CONSTRAINT "note_type_lk_pkey" PRIMARY KEY ("note_type_id")
);

-- CreateTable
CREATE TABLE "note_section_lk" (
    "note_section_id" UUID NOT NULL,
    "section_name" TEXT NOT NULL,

    CONSTRAINT "note_section_lk_pkey" PRIMARY KEY ("note_section_id")
);

-- CreateTable
CREATE TABLE "note_type_sections" (
    "note_type_section_id" UUID NOT NULL,
    "note_type_id" UUID NOT NULL,
    "note_section_id" UUID NOT NULL,

    CONSTRAINT "note_type_sections_pkey" PRIMARY KEY ("note_type_section_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "note_type_lk_note_type_name_key" ON "note_type_lk"("note_type_name");

-- CreateIndex
CREATE UNIQUE INDEX "note_section_lk_section_name_key" ON "note_section_lk"("section_name");

-- AddForeignKey
ALTER TABLE "note_header" ADD CONSTRAINT "note_header_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "patient"("patient_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "note_header" ADD CONSTRAINT "note_header_encounter_id_fkey" FOREIGN KEY ("encounter_id") REFERENCES "encounter"("encounter_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "note_header" ADD CONSTRAINT "note_header_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "doctor"("doctor_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "note_header" ADD CONSTRAINT "note_header_note_type_id_fkey" FOREIGN KEY ("note_type_id") REFERENCES "note_type_lk"("note_type_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "note_detail" ADD CONSTRAINT "note_detail_note_header_id_fkey" FOREIGN KEY ("note_header_id") REFERENCES "note_header"("note_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "note_detail" ADD CONSTRAINT "note_detail_note_section_id_fkey" FOREIGN KEY ("note_section_id") REFERENCES "note_section_lk"("note_section_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "note_type_sections" ADD CONSTRAINT "note_type_sections_note_type_id_fkey" FOREIGN KEY ("note_type_id") REFERENCES "note_type_lk"("note_type_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "note_type_sections" ADD CONSTRAINT "note_type_sections_note_section_id_fkey" FOREIGN KEY ("note_section_id") REFERENCES "note_section_lk"("note_section_id") ON DELETE RESTRICT ON UPDATE CASCADE;
