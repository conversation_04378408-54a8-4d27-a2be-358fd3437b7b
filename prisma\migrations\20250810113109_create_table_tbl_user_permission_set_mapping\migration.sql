-- CreateTable
CREATE TABLE "tbl_user_permission_set_mapping" (
    "user_perm_set_map_id" UUID NOT NULL,
    "user_detail_id" UUID NOT NULL,
    "permission_set_id" UUID NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_user_permission_set_mapping_pkey" PRIMARY KEY ("user_perm_set_map_id")
);

-- AddForeignKey
ALTER TABLE "tbl_user_permission_set_mapping" ADD CONSTRAINT "tbl_user_permission_set_mapping_user_detail_id_fkey" FOREIGN KEY ("user_detail_id") REFERENCES "tbl_user_details"("user_detail_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_user_permission_set_mapping" ADD CONSTRAINT "tbl_user_permission_set_mapping_permission_set_id_fkey" FOREIGN KEY ("permission_set_id") REFERENCES "tbl_permission_set"("perm_set_id") ON DELETE RESTRICT ON UPDATE CASCADE;
