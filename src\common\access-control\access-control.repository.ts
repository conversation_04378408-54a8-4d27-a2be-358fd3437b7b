import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UtilsService } from '../utils/utils.service';

@Injectable()
export class AccessControlRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}
  private readonly logger = new Logger(AccessControlRepository.name);

  async getUserPermissions(userEmail: string) {
    try {
      const result = await this.prisma.tbl_user_details.findMany({
        where: {
          email: userEmail,
          isActive: true,
        },
        include: {
          tbl_user_permission_mapping: {
            where: {
              isActive: true,
            },
            include: {
              tbl_permission_lk: true,
            },
          },
          tbl_user_role_mapping: {
            where: {
              isActive: true,
            },
            include: {
              roleLookup: {
                include: {
                  tbl_role_permission_mapping: {
                    where: {
                      isActive: true,
                    },
                    include: {
                      tbl_permission_lk: true,
                    },
                  },
                },
              },
            },
          },
        },
      });
      return result;
    } catch (error) {
      this.logger.error('Error fetching user permissions:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch user permissions',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
