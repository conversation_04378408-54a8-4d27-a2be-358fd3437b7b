const fs = require('fs');
const path = require('path');

/**
 * Permission seed function
 * @param {import('@prisma/client').PrismaClient} prisma
 */
module.exports = async function (prisma) {
  const filePath = path.join(__dirname, 'roles_permission.json');
  const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'));


  const permissions = await prisma.tbl_permission_lk.findMany();
  const roles = await prisma.tbl_role_lk.findMany();

  // Build lookup maps for easy access
  const permissionMap = {};
  permissions.forEach((p) => {
    permissionMap[p.key] = p.permission_id;
  });

  const roleMap = {};
  roles.forEach(r => {
    roleMap[r.role_name] = r.role_id;
  });

  // Collect mappings
  const mappings = [];
  // Iterate roles from JSON and insert mappings
  for (const roleObj of data.roles) {
    const roleId = roleMap[roleObj.role];
    if (!roleId) {
      console.warn(`⚠️ Role not found in DB: ${roleObj.role}`);
      continue;
    }
    for (const permKey of roleObj.permissions) {
      const permId = permissionMap[permKey];
      if (!permId) {
        console.warn(`⚠️ Permission not found in DB: ${permKey}`);
        continue;
      }
      mappings.push({
        role_id: roleId,
        permission_id: permId,
      });
    }
  }
  // Remove duplicates (role_id + permission_id)
  const uniqueMappings = Array.from(
    new Map(mappings.map(m => [`${m.role_id}_${m.permission_id}`, m])).values()
  );

  let res;
  // Bulk insert
  if (uniqueMappings.length > 0) {
    res = await prisma.tbl_role_permission_mapping.createMany({
      data: uniqueMappings,
      skipDuplicates: true, // ✅ avoids unique constraint errors if exists
    });
  }

  console.log(`${res.count} Roles & permissions seeding completed ✅`);
};
