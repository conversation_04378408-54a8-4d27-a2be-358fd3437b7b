import { Injectable, Logger } from '@nestjs/common';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { tbl_permission_lk } from '@prisma/client';
import { PermissionsRepository } from './permissions.repository';

@Injectable()
export class PermissionsService {
  private readonly logger = new Logger(PermissionsService.name);
  constructor(
    private readonly permissionsRepository: PermissionsRepository,
  ) { }

  async createPermission(data: CreatePermissionDto, userEmail: string): Promise<tbl_permission_lk[]> {
    return await this.permissionsRepository.createPermission(data, userEmail);
  }

  async findAll() {
    return await this.permissionsRepository.getPermissions();
  }
}
