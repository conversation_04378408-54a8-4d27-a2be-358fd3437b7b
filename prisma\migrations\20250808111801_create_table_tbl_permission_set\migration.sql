-- CreateEnum
CREATE TYPE "PermissionType" AS ENUM ('user', 'system');

-- CreateTable
CREATE TABLE "tbl_permission_set" (
    "perm_set_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "permission_type" "PermissionType" NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_permission_set_pkey" PRIMARY KEY ("perm_set_id")
);

-- CreateTable
CREATE TABLE "tbl_role_permission_set_mapping" (
    "role_per_set_map_id" UUID NOT NULL,
    "role_lk_id" UUID NOT NULL,
    "perm_set_id" UUID NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_role_permission_set_mapping_pkey" PRIMARY KEY ("role_per_set_map_id")
);

-- AddForeignKey
ALTER TABLE "tbl_role_permission_set_mapping" ADD CONSTRAINT "tbl_role_permission_set_mapping_role_lk_id_fkey" FOREIGN KEY ("role_lk_id") REFERENCES "tbl_role_lk"("role_id") ON DELETE RESTRICT ON UPDATE CASCADE;
