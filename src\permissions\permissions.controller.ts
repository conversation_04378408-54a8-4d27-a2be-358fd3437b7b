import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { PermissionsService } from './permissions.service';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { ApiResponseDTO } from 'src/common/dtos';
import { tbl_permission_lk } from '@prisma/client';

@Controller('permissions')
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) { }

  @Post('create-permission')
  async createPermissions(@Body() data: CreatePermissionDto, userEmail: string): Promise<ApiResponseDTO<tbl_permission_lk[]>> {
    return { data: await this.permissionsService.createPermission(data, userEmail) }
  }

  @Get()
  async findAllPermissions() {
    return await this.permissionsService.findAll();
  }
}
