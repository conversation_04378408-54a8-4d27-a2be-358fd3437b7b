import { HttpStatus, Injectable, NotFoundException, Logger } from '@nestjs/common';
import { LabsRepository } from './labs.repository';
import { Labs } from '@prisma/client';
import { CreateLabDto, LabDetailsDto, UpdateLabDto } from './dtos/labs.dto';
import { TranscribeService } from 'src/transcribe/transcribe.service';
import { TranscriptionsRepository } from 'src/transcriptions/transcriptions.repository';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';
import {
  LLM_RESPONSETYPES,
  TRANSCRIPTION_TYPES,
} from 'src/common/constants/common.constants';

@Injectable()
export class LabsService {
  private readonly logger = new Logger(LabsService.name);
  constructor(
    private readonly labsrepository: LabsRepository,
    private readonly transcribeService: TranscribeService,
    private readonly transcriptionsRepository: TranscriptionsRepository,
    private readonly prismaservice: PrismaService,
    private readonly utilsService: UtilsService,
  ) { }
  async getLabsByEncounterId(encounterId: string): Promise<Labs[]> {
    return await this.labsrepository.getLabsByEncounterId(encounterId);
  }

  async createLab(data: CreateLabDto, userEmail: string): Promise<Labs[]> {
    return await this.labsrepository.createLab(data, userEmail);
  }

  async updateLab(
    lab_id: string,
    data: UpdateLabDto,
    userEmail: string,
  ): Promise<Labs> {
    return await this.labsrepository.updateLab(lab_id, data, userEmail);
  }

  async deleteLab(lab_id: string, userEmail: string): Promise<Labs> {
    return await this.labsrepository.deleteLab(lab_id, userEmail);
  }

  async generateLabFromTranscription(
    encounter_id: string,
    userEmail: string,
  ): Promise<any> {
    try {
      const existingLabs =
        await this.labsrepository.getLabsByEncounterId(encounter_id);
      // if labs already exist for this encounter, return them
      if (existingLabs.length > 0) {
        return { Labs: existingLabs };
      }
      // get transcription for encounter
      const transcription =
        await this.transcriptionsRepository.getTranscriptionUsingEncounterId(
          encounter_id,
        );

      // if no transcription exists for this encounter, throw error
      if (!transcription) {
        this.logger.error(`No transcription found for encounter ID: ${encounter_id}`);
        throw new NotFoundException(
          'No transcription found for this encounter',
        );
      }

      const combinedTranscription = transcription
        .filter((tr) => tr.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL)
        .map((tr) => tr.transcription)
        .filter((t) => t)
        .join(' ');

      // generate lab from transcription
      const labsdata = await this.utilsService.genereteResponseFromLLM(
        'gpt-4o',
        'user',
        `${combinedTranscription} Based on the provided transcript, generate two structured outputs in JavaScript JSON format. First, create a Labs table that includes only the lab tests explicitly mentioned by the doctor in the conversation. This table should contain three fields: Lab Name (the name of the ordered test), Notes (a brief description of its purpose), and Date Ordered (the inferred date from the transcript). Second, generate a Suggested Labs table that includes only additional lab tests that may be clinically relevant based on the patient's symptoms but were not mentioned in the transcript. This table should include Lab Name, Notes (a brief description of the test), and Reason (a clear justification directly linked to the patient's reported symptoms). Avoid listing excessive or unnecessary tests, ensuring that each suggested lab has a strong clinical basis. Provide the response in pure JavaScript JSON format that can be directly parsed. Do not include Markdown, special characters, or formatting outside the JSON structure`,
        1500,
        0.7,
        LLM_RESPONSETYPES.JSON,
        'LABS',
        transcription
          .filter(
            (tr) => tr.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL,
          )
          .map((tr) => tr.id),
        userEmail,
      );

      if (!labsdata) {
        this.logger.error(`Failed to generate labs data from LLM for encounter ID: ${encounter_id}`);
        throw new Error('Error generating labs from transcription');
      }

      let parsedJson = JSON.parse(labsdata);
      if (!parsedJson) {
        this.logger.error(`Error parsing labs JSON for encounter ID: ${encounter_id}`);
        throw new Error(
          'Error generating labs from transcription json format not correct',
        );
      }
      // let actualjson = JSON.parse(parsedJson.response);

      let actualjson: any;

      if (this.utilsService.IsParsableJson(parsedJson)) {
        actualjson = JSON.parse(parsedJson);
      } else {
        actualjson = parsedJson;
      }

      let ldata =
        actualjson['Labs'] ||
        actualjson['labs'] ||
        actualjson['Labs Table'] ||
        actualjson['labs table'];
      let sdata =
        actualjson['Suggested Labs'] ||
        actualjson['suggested Labs'] ||
        actualjson['Suggested labs'] ||
        actualjson['suggested labs'];

      let data: { Labs: LabDetailsDto[]; suggestedLabs: LabDetailsDto[] } = {
        Labs:
          ldata && ldata.length > 0
            ? ldata.map((lab) => ({
              lab_name: lab['Lab Name'],
              notes: lab.Notes,
              dateOrdered: transcription[0].created_at.toISOString(),
              encounter_id: encounter_id,
              reason: '',
              status: 'PENDING',
              created_by: 'Transcript',
            }))
            : [],
        suggestedLabs:
          sdata && sdata.length > 0
            ? sdata.map((lab) => ({
              lab_name: lab['Lab Name'],
              notes: lab.Notes,
              reason: lab.Reason,
              dateOrdered: transcription[0].created_at.toISOString(),
              encounter_id: encounter_id,
              status: 'PENDING',
              created_by: 'Suggested',
            }))
            : [],
      };

      let createlab: CreateLabDto = {
        info: [...data.Labs, ...data.suggestedLabs],
      };
      // create labs in database
      const createdlabs = await this.labsrepository.createLab(
        createlab,
        userEmail,
      );
      if (createdlabs.length == 0) {
        this.logger.error(`Failed to create labs in database for encounter ID: ${encounter_id}`);
        throw new Error('Error creating labs in db');
      }

      return { Labs: createdlabs };
    } catch (error) {
      this.logger.error(`Error generating lab from transcription for encounter ID: ${encounter_id}:`, error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to generate lab from transcription',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async upadateLabStatusByLabid(
    lab_id: string,
    status_value: string,
    userEmail: string,
    status_reason: string,
  ) {
    const existingLab = await this.prismaservice.labs.count({
      where: {
        lab_id: lab_id,
        isActive: true,
      },
    });

    if (existingLab === 0) {
      this.logger.error(`Lab not found for the given lab ID: ${lab_id}`);
      throw new NotFoundException('Lab not found');
    }

    const existingStatus = await this.prismaservice.status.count({
      where: {
        isActive: true,
        status: {
          equals: status_value,
        },
      },
    });

    if (existingStatus === 0) {
      this.logger.error(`Status not found for the given value: ${status_value}`);
      throw new NotFoundException('status not found');
    }
    return await this.labsrepository.upadateLabStatusByLabid(
      lab_id,
      status_value,
      userEmail,
      status_reason,
    );
  }
}
