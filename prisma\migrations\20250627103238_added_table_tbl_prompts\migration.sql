-- CreateTable
CREATE TABLE "tbl_prompts" (
    "prompt_id" UUID NOT NULL,
    "prompt_user" TEXT NOT NULL,
    "prompt_system" TEXT,
    "name" TEXT,
    "description" TEXT,
    "api_name" TEXT,
    "version" TEXT NOT NULL DEFAULT '1.0',
    "temperature" DOUBLE PRECISION DEFAULT 0.7,
    "max_tokens" INTEGER,
    "top_p" DOUBLE PRECISION,
    "frequency_penalty" DOUBLE PRECISION,
    "presence_penalty" DOUBLE PRECISION,
    "model_name" TEXT NOT NULL,
    "prompt_variables" TEXT,
    "default_values" JSONB,
    "additional_info" JSONB,
    "category" TEXT,
    "tags" TEXT,
    "usage_count" INTEGER DEFAULT 0,
    "last_used" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "is_template" BOOLEAN DEFAULT false,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_prompts_pkey" PRIMARY KEY ("prompt_id")
);
