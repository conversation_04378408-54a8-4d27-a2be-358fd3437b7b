import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, IsUUID, IsBoolean, ValidateNested } from 'class-validator';


export class CreatePermissionDto {
    @Type(() => PermissionDto)
    @ValidateNested({ each: true })
    info: PermissionDto[];
}
export class PermissionDto {
    @ApiProperty({
        description: 'Unique key for the permission',
        example: 'USER_CREATE',
    })
    @IsNotEmpty({ message: 'Key is required' })
    @IsString({ message: 'Key must be a string' })
    key: string;

    @ApiProperty({
        description: 'Description of the permission',
        example: 'Permission to create a new user',
    })
    @IsOptional()
    @IsString({ message: 'Description must be a string' })
    description?: string;

    @ApiProperty({
        description: 'Type of permission (default SYSTEM_DEFINED)',
        example: 'SYSTEM_DEFINED',
    })
    @IsOptional()
    @IsString({ message: 'Type must be a string' })
    type?: string;

    @ApiProperty({
        description: 'Permission type (domain-specific classification)',
        example: 'READ | WRITE | DELETE',
    })
    @IsOptional()
    @IsString({ message: 'Permission type must be a string' })
    permission_type?: string;

    @ApiProperty({
        description: 'Tenant ID if applicable',
        example: '6fd6be04-e7e3-448b-8dc3-dbc6636563a3',
    })
    @IsOptional()
    @IsUUID('4', { message: 'Tenant id must be a valid UUID' })
    tenant_id?: string;

    @ApiProperty({
        description: 'Whether the permission is active',
        example: true,
        default: true,
    })
    @IsOptional()
    @IsBoolean({ message: 'isActive must be a boolean' })
    isActive?: boolean;

    @ApiProperty({
        description: 'Created by user',
        example: 'ADMIN',
    })
    @IsOptional()
    @IsString({ message: 'Created by must be a string' })
    created_by?: string;
}

