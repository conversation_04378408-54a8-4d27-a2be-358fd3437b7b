import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsUUID, IsOptional, IsString, IsBoolean } from 'class-validator';

export class CreateUserPermissionMappingDto {
  @ApiProperty({
    description: 'UUID of the user',
    example: 'a3e56c48-59fb-4a5c-8d29-42d87b9af8a1',
  })
  @IsUUID('4', { message: 'User ID must be a valid UUID' })
  user_id: string;

  @ApiProperty({
    description: 'UUID of the permission',
    example: 'f2a3b6d8-19d3-4e77-8c2e-91c3c42f23a9',
  })
  @IsUUID('4', { message: 'Permission ID must be a valid UUID' })
  permission_id: string;

  @ApiPropertyOptional({
    description: 'Type of permission mapping',
    example: 'SYSTEM_DEFINED',
    default: 'SYSTEM_DEFINED',
  })
  @IsOptional()
  @IsString({ message: 'Type must be a string' })
  type?: string;

  @ApiPropertyOptional({
    description: 'Indicates if the mapping is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'isActive must be a boolean value' })
  isActive?: boolean;
}
