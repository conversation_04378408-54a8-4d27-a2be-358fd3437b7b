import { Modu<PERSON> } from '@nestjs/common';
import { TranscriptionsController } from './transcriptions.controller';
import { TranscriptionsRepository } from './transcriptions.repository';
import { TranscriptionsService } from './transcriptions.service';
import { EncounterService } from 'src/encounter/encounter.service';
import { EncounterRepository } from 'src/encounter/encounter.repository';
import { EncounterModule } from 'src/encounter/encounter.module';

@Module({
  imports: [EncounterModule],
  controllers: [TranscriptionsController],
  providers: [TranscriptionsRepository, TranscriptionsService,],
})
export class TranscriptionsModule {}
