import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class UniversalPicklistRepository {
  private readonly logger = new Logger(UniversalPicklistRepository.name);
  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async GetUniversalPicklistData({
    master_code,
    parent_record_id,
    domain,
  }: {
    master_code: string;
    parent_record_id: string[];
    domain?: string;
  }): Promise<any> {
    try {
      if (!master_code) {
        throw new Error('Please provide master_code');
      }
      if (parent_record_id?.length == 0) {
        parent_record_id = undefined;
      } else {
        parent_record_id = parent_record_id.filter(Boolean);
      }
      const picklistData =
        await this.prisma.tbl_universal_picklist_data.findMany({
          where: {
            master_code: {
              equals: master_code,
              mode: 'insensitive',
            },
            isActive: true,
            ...(parent_record_id?.length > 0 && {
              parent_record_id: {
                in: parent_record_id,
              },
            }),
            ...(domain && {
              additional_info: {
                path: ['domain'],
                equals: domain,
              },
            }),
          },
          select: {
            record_id: true,
            record_name: true,
            parent_record_id: true,
            additional_info: true,
          },
        });
      return picklistData;
    } catch (error) {
      this.logger.error(
        'Error fetching universal picklist data:',
        error?.stack,
      );

      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch universal picklist data',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
