{"permissions": [{"key": "patient.read.self", "description": "Patient Read Self"}, {"key": "patient.read.org", "description": "Patient Read Org"}, {"key": "patient.update.self", "description": "Patient Update Self"}, {"key": "patient.update.org", "description": "Patient Update Org"}, {"key": "patient.create.self", "description": "Patient Create Self"}, {"key": "patient.create.org", "description": "Patient Create Org"}, {"key": "patient.delete.self", "description": "Patient Delete Self"}, {"key": "patient.delete.org", "description": "Patient Delete Org"}, {"key": "visit.read.self", "description": "<PERSON><PERSON><PERSON> Read Self"}, {"key": "visit.read.org", "description": "Visit Read Org"}, {"key": "visit.create.self", "description": "Visit Create Self"}, {"key": "visit.create.org", "description": "Visit Create Org"}, {"key": "visit.update.self", "description": "Visit Update Self"}, {"key": "visit.update.org", "description": "Visit Update Org"}, {"key": "visit.delete.self", "description": "Visit Delete Self"}, {"key": "visit.delete.org", "description": "Visit Delete Org"}, {"key": "recording.read.self", "description": "Recording Read Self"}, {"key": "recording.read.org", "description": "Recording Read Org"}, {"key": "recording.create.self", "description": "Recording Create Self"}, {"key": "recording.create.org", "description": "Recording Create Org"}, {"key": "recording.update.self", "description": "Recording Update Self"}, {"key": "recording.update.org", "description": "Recording Update Org"}, {"key": "recording.delete.self", "description": "Recording Delete Self"}, {"key": "recording.delete.org", "description": "Recording Delete Org"}, {"key": "prescription.read.self", "description": "Prescription Read Self", "permission_type": "BASIC"}, {"key": "prescription.read.org", "description": "Prescription Read Org", "permission_type": "BASIC"}, {"key": "prescription.create.self", "description": "Prescription Create Self"}, {"key": "prescription.create.org", "description": "Prescription Create Org"}, {"key": "prescription.update.self", "description": "Prescription Update Self"}, {"key": "prescription.update.org", "description": "Prescription Update Org"}, {"key": "prescription.delete.self", "description": "Prescription Delete Self"}, {"key": "prescription.delete.org", "description": "Prescription Delete Org"}, {"key": "prescription.setting.create.self", "description": "Prescription Setting Create Self"}, {"key": "prescription.setting.create.org", "description": "Prescription Setting Create Org"}, {"key": "prescription.setting.read.self", "description": "Prescription Setting Read Self"}, {"key": "prescription.setting.read.org", "description": "Prescription Setting Read Org"}, {"key": "prescription.setting.update.self", "description": "Prescription Setting Update Self"}, {"key": "prescription.setting.update.org", "description": "Prescription Setting Update Org"}, {"key": "prescription.setting.delete.self", "description": "Prescription Setting Delete Self"}, {"key": "prescription.setting.delete.org", "description": "Prescription Setting Delete Org"}, {"key": "lab.read.self", "description": "Lab Read Self", "permission_type": "BASIC"}, {"key": "lab.read.org", "description": "Lab Read Org", "permission_type": "BASIC"}, {"key": "lab.create.self", "description": "Lab Create Self"}, {"key": "lab.create.org", "description": "Lab Create Org"}, {"key": "lab.update.self", "description": "Lab Update Self"}, {"key": "lab.update.org", "description": "Lab Update Org"}, {"key": "lab.delete.self", "description": "Lab Delete Self"}, {"key": "lab.delete.org", "description": "Lab Delete Org"}, {"key": "lab.suggested.view.self", "description": " Suggested Lab View Self"}, {"key": "lab.suggested.view.org", "description": "Suggested Lab View Org"}, {"key": "lab.setting.view.self", "description": "Lab Setting View Self"}, {"key": "lab.setting.view.org", "description": "Lab Setting View Org"}, {"key": "lab.setting.create.self", "description": "Lab Setting Create Self"}, {"key": "lab.setting.create.org", "description": "Lab Setting Create Org"}, {"key": "lab.setting.update.self", "description": "Lab Setting Update Self"}, {"key": "lab.setting.update.org", "description": "Lab Setting Update Org"}, {"key": "lab.setting.delete.self", "description": "Lab Setting Delete Self"}, {"key": "lab.setting.delete.org", "description": "Lab Setting Delete Org"}, {"key": "settings.read.self", "description": "<PERSON><PERSON><PERSON> Read Self"}, {"key": "settings.read.org", "description": "Settings Read Org"}, {"key": "settings.update.self", "description": "Settings Update Self"}, {"key": "settings.update.org", "description": "Settings Update Org"}, {"key": "department.read.org", "description": "Department Read Org"}, {"key": "department.read.self", "description": "Department Read Self"}, {"key": "department.create.org", "description": "Department Create Org"}, {"key": "department.update.org", "description": "Department Update Org"}, {"key": "department.update.self", "description": "Department Update Self"}, {"key": "department.delete.org", "description": "Department Delete Org"}, {"key": "department.delete.self", "description": "Department Delete Self"}, {"key": "vitals.read.self", "description": "<PERSON><PERSON> Read Self", "permission_type": "BASIC"}, {"key": "vitals.read.org", "description": "Vitals Read Org", "permission_type": "BASIC"}, {"key": "vitals.create.self", "description": "Vitals Create Self"}, {"key": "vitals.create.org", "description": "Vitals Create Org"}, {"key": "vitals.update.self", "description": "Vitals Update Self", "permission_type": "BASIC"}, {"key": "vitals.update.org", "description": "Vitals Update Org", "permission_type": "BASIC"}, {"key": "vitals.delete.self", "description": "Vitals Delete Self"}, {"key": "vitals.delete.org", "description": "Vitals Delete Org"}, {"key": "vitals.setting.read.self", "description": "Vitals Setting Read Self"}, {"key": "vitals.setting.read.org", "description": "Vitals Setting Read Org"}, {"key": "vitals.setting.create.self", "description": "Vitals Setting Create Self"}, {"key": "vitals.setting.create.org", "description": "Vitals Setting Create Org"}, {"key": "vitals.setting.update.self", "description": "Vitals Setting Update Self"}, {"key": "vitals.setting.update.org", "description": "Vitals Setting Update Org"}, {"key": "vitals.setting.delete.self", "description": "Vitals Setting Delete Self"}, {"key": "vitals.setting.delete.org", "description": "Vitals Setting Delete Org"}, {"key": "advice.read.self", "description": "Advice Read Self", "permission_type": "BASIC"}, {"key": "advice.read.org", "description": "Advice Read Org", "permission_type": "BASIC"}, {"key": "advice.create.self", "description": "Advice Create Self"}, {"key": "advice.create.org", "description": "Advice Create Org"}, {"key": "advice.update.self", "description": "Advice Update Self"}, {"key": "advice.update.org", "description": "Advice Update Org"}, {"key": "context.read.self", "description": "Context Read Self", "permission_type": "BASIC"}, {"key": "context.read.org", "description": "Context Read Org", "permission_type": "BASIC"}, {"key": "context.create.self", "description": "Context Create Self", "permission_type": "BASIC"}, {"key": "context.create.org", "description": "Context Create Org", "permission_type": "BASIC"}, {"key": "context.update.self", "description": "Context Update Self", "permission_type": "BASIC"}, {"key": "context.update.org", "description": "Context Update Org", "permission_type": "BASIC"}, {"key": "context.delete.self", "description": "Context Delete Self", "permission_type": "BASIC"}, {"key": "context.delete.org", "description": "Context Delete Org", "permission_type": "BASIC"}, {"key": "note.view.self", "description": "Note View Self", "permission_type": "BASIC"}, {"key": "note.view.org", "description": "Note View Org", "permission_type": "BASIC"}, {"key": "note.create.self", "description": "Note Create Self"}, {"key": "note.create.org", "description": "Note Create Org"}, {"key": "note.update.self", "description": "Note Update Self"}, {"key": "note.update.org", "description": "Note Update Org"}, {"key": "note.delete.self", "description": "Note Delete Self"}, {"key": "note.delete.org", "description": "Note Delete Org"}, {"key": "note.export.self", "description": "Note Export Self", "permission_type": "BASIC"}, {"key": "note.export.org", "description": "Note Export Org", "permission_type": "BASIC"}, {"key": "setting.export.self", "description": "Setting Export Self"}, {"key": "setting.export.org", "description": "Setting Export Org"}, {"key": "visit.signoff.self", "description": "Visit Sign Off Self"}, {"key": "visit.signoff.org", "description": "Visit Sign Off Org"}, {"key": "note.setting.read.self", "description": "Note Settings Read Self"}, {"key": "note.setting.read.org", "description": "Note Settings Read Org"}, {"key": "note.settting.create.self", "description": " Note Setting Create Self"}, {"key": "note.settting.create.org", "description": " Note Setting Create Org"}, {"key": "note.setting.update.org", "description": "Note Settings Update Org"}, {"key": "note.setting.update.self", "description": "Note Settings Update Self"}, {"key": "note.setting.delete.self", "description": "Note Setting Delete Self"}, {"key": "note.setting.delete.org", "description": "Note Setting Delete Org"}, {"key": "transcript.view.self", "description": "Transcript View Self"}, {"key": "transcript.view.org", "description": "Transcript View Org"}, {"key": "transcript.setting.read.self", "description": "Transcript Settings Read Self"}, {"key": "transcript.setting.read.org", "description": "Transcript Settings Read Org"}, {"key": "transcript.setting.create.self", "description": "Transcript Setting Create Self"}, {"key": "transcript.setting.create.org", "description": "Transcript Setting Create Org"}, {"key": "transcript.setting.update.self", "description": "Transcript Settings Update Self"}, {"key": "transcript.setting.update.org", "description": "Transcript Settings Update Org"}, {"key": "transcript.setting.delete.self", "description": "Transcript Setting Delete Self"}, {"key": "transcript.setting.delete.org", "description": "Transcript Setting Delete Org"}, {"key": "context.setting.read.self", "description": "Context Setting Read Self"}, {"key": "context.setting.read.org", "description": "Context Setting Read Org"}, {"key": "context.setting.create.self", "description": "Context Setting Create Self"}, {"key": "context.setting.create.org", "description": "Context Setting Create Org"}, {"key": "context.setting.update.self", "description": "Context Settings Update Self"}, {"key": "context.setting.update.org", "description": "Context Settings Update Org"}, {"key": "context.setting.delete.self", "description": "Context Setting Delete Self"}, {"key": "context.setting.delete.org", "description": "Context Setting Delete Org"}, {"key": "doctor.read.self", "description": "Doctor <PERSON>"}, {"key": "doctor.read.org", "description": "Doctor <PERSON>"}, {"key": "doctor.create.self", "description": "Doctor Create Self"}, {"key": "doctor.create.org", "description": "Doctor <PERSON><PERSON>"}, {"key": "doctor.update.self", "description": "Doctor Update Self"}, {"key": "doctor.update.org", "description": "Doctor Update Org"}, {"key": "doctor.delete.self", "description": "Doctor Delete Self"}, {"key": "doctor.delete.org", "description": "Doctor <PERSON><PERSON>"}, {"key": "beta.feature.setting.read.self", "description": "Beta Feature Setting Read Self"}, {"key": "beta.feature.setting.read.org", "description": "Beta Feature Setting Read Org"}, {"key": "beta.feature.setting.create.self", "description": "Beta Feature Setting Create Self"}, {"key": "beta.feature.setting.create.org", "description": "Beta Feature Setting Create Org"}, {"key": "beta.feature.setting.update.self", "description": "Beta Feature Setting Update Self"}, {"key": "beta.feature.setting.update.org", "description": "Beta Feature Setting Update Org"}, {"key": "beta.feature.setting.delete.self", "description": "Beta Feature Setting Delete Self"}, {"key": "beta.feature.setting.delete.org", "description": "Beta Feature Setting Delete Org"}, {"key": "profile.setting.read.self", "description": "Profile Setting Read Self"}, {"key": "profile.setting.read.org", "description": "Profile Setting Read Org"}, {"key": "profile.setting.create.self", "description": "Profile Setting Create Self"}, {"key": "profile.setting.create.org", "description": "Profile Setting Create Org"}, {"key": "profile.setting.update.self", "description": "Profile Setting Update Self"}, {"key": "profile.setting.update.org", "description": "Profile Setting Update Org"}, {"key": "profile.setting.delete.self", "description": "Profile Setting Delete Self"}, {"key": "profile.setting.delete.org", "description": "Profile Setting Delete Org"}, {"key": "ai-assistance.access.self", "description": "AI Assistance Access Self"}, {"key": "ai-assistance.access.org", "description": "AI Assistance Access Org"}, {"key": "assign.permissions.basic", "description": "Assign Basic Permissions"}, {"key": "assign.permissions.all", "description": "Assign all Permissions"}, {"key": "user.read.self", "description": "User Read Self"}, {"key": "user.read.org", "description": "User Read Org"}, {"key": "user.create.self", "description": "User Create Self"}, {"key": "user.create.org", "description": "User Create Org"}, {"key": "user.update.self", "description": "User Update Self"}, {"key": "user.update.org", "description": "User Update Org"}, {"key": "user.delete.self", "description": "User Delete Self"}, {"key": "user.delete.org", "description": "User Delete Org"}, {"key": "subscription.create.self", "description": "Create Subscription Self"}, {"key": "subscription.create.org", "description": "Create Subscription Org"}, {"key": "subscription.read.self", "description": "Read Subscription Self"}, {"key": "subscription.read.org", "description": "Read Subscription Org"}, {"key": "subscription.update.self", "description": "Update Subscription Self"}, {"key": "subscription.update.org", "description": "Update Subscription Org"}, {"key": "subscription.delete.self", "description": "Delete Subscription Self"}, {"key": "subscription.delete.org", "description": "Delete Subscription Org"}, {"key": "letterhead.setting.read.self", "description": "Letterhead Setting Read Self"}, {"key": "letterhead.setting.read.org", "description": "Letterhead Setting Read Org"}, {"key": "letterhead.setting.create.self", "description": "Letterhead Setting Create Self"}, {"key": "letterhead.setting.create.org", "description": "Letterhead Setting Create Org"}, {"key": "letterhead.setting.update.self", "description": "Letterhead Setting Update Self"}, {"key": "letterhead.setting.update.org", "description": "Letterhead Setting Update Org"}, {"key": "letterhead.setting.delete.self", "description": "Letterhead Setting Delete Self"}, {"key": "letterhead.setting.delete.org", "description": "Letterhead Setting Delete Org"}]}