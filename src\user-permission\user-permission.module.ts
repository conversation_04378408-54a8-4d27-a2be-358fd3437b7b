import { Module } from '@nestjs/common';
import { UserPermissionController } from './user-permission.controller';
import { UserPermissionService } from './user-permission.service';
import { UserPermissionRepository } from './user-permission.repository';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';

@Module({
  controllers: [UserPermissionController],
  providers: [UserPermissionService,
    UserPermissionRepository,
    PrismaService,
    UtilsService,]
})
export class UserPermissionModule {}
