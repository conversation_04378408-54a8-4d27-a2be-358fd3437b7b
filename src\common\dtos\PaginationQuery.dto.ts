import { IsInt, Min } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class PaginationQueryDTO {
  @IsInt({ message: 'page must be an integer number' })
  @Min(1, { message: 'page must be a positive integer number' })
  @Transform(({ value }) => (value !== undefined ? Number(value) : 1))
  page: number = 1;

  @IsInt({ message: 'limit must be an integer number' })
  @Min(1, { message: 'limit must be a positive integer number' })
  @Transform(({ value }) => (value !== undefined ? Number(value) : 10))
  limit: number = 10;
}
