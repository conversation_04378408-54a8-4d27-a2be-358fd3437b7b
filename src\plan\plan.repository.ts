import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Plan } from '@prisma/client';
import { skip } from 'rxjs';
import { PaginatedResultDTO } from 'src/common/dtos';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class PlanRepository {
  private readonly logger = new Logger(PlanRepository.name);

  constructor(
    private readonly prismaservice: PrismaService,
    private readonly utilservice: UtilsService,
  ) {}

  async getAllPlans(
    page: number = 0,
    limit: number = 10,
  ): Promise<PaginatedResultDTO<Plan>> {
    try {
      let where = {
        isActive: true,
      };
      const [plans, totalRecords] = await this.prismaservice.$transaction([
        this.prismaservice.plan.findMany({
          where: where,
          skip:(page-1)*limit,
          take:limit
        },
      ),
        this.prismaservice.plan.count({
          where: where,
          skip: (page - 1) * limit,
          take: limit,
        }),
      ]);

      const pagination = this.utilservice.getPagination(
        page,
        limit,
        totalRecords,
      );

      return {
        data: plans,
        pagination: pagination,
      };
    } catch (error) {
      this.logger.error('Error fetching plan:', error?.stack);
      throw error;
    }
  }
}
