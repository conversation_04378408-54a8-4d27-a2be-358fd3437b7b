-- Migration: Insert all countries phone codes into tbl_universal_picklist_data
-- Comprehensive list with all countries using flagcdn.com flag URLs

INSERT INTO ekkomd_core.tbl_universal_picklist_data (
    "Id",
    "master_code",
    "master_code_prefix", 
    "record_name",
    "additional_info",
    "picklist_id",
    "created_at"
) VALUES 
-- Zone 1 - North America
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"United States","flag":"https://flagcdn.com/w320/us.jpg","country_code":"US"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Canada","flag":"https://flagcdn.com/w320/ca.jpg","country_code":"CA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),

-- Zone 2 - Africa and others
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+20',
    '{"country":"Egypt","flag":"https://flagcdn.com/w320/eg.jpg","country_code":"EG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+211',
    '{"country":"South Sudan","flag":"https://flagcdn.com/w320/ss.jpg","country_code":"SS"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+212',
    '{"country":"Morocco","flag":"https://flagcdn.com/w320/ma.jpg","country_code":"MA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+213',
    '{"country":"Algeria","flag":"https://flagcdn.com/w320/dz.jpg","country_code":"DZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+216',
    '{"country":"Tunisia","flag":"https://flagcdn.com/w320/tn.jpg","country_code":"TN"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+218',
    '{"country":"Libya","flag":"https://flagcdn.com/w320/ly.jpg","country_code":"LY"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+220',
    '{"country":"Gambia","flag":"https://flagcdn.com/w320/gm.jpg","country_code":"GM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+221',
    '{"country":"Senegal","flag":"https://flagcdn.com/w320/sn.jpg","country_code":"SN"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+222',
    '{"country":"Mauritania","flag":"https://flagcdn.com/w320/mr.jpg","country_code":"MR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+223',
    '{"country":"Mali","flag":"https://flagcdn.com/w320/ml.jpg","country_code":"ML"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+224',
    '{"country":"Guinea","flag":"https://flagcdn.com/w320/gn.jpg","country_code":"GN"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+225',
    '{"country":"Ivory Coast","flag":"https://flagcdn.com/w320/ci.jpg","country_code":"CI"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+226',
    '{"country":"Burkina Faso","flag":"https://flagcdn.com/w320/bf.jpg","country_code":"BF"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+227',
    '{"country":"Niger","flag":"https://flagcdn.com/w320/ne.jpg","country_code":"NE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+228',
    '{"country":"Togo","flag":"https://flagcdn.com/w320/tg.jpg","country_code":"TG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+229',
    '{"country":"Benin","flag":"https://flagcdn.com/w320/bj.jpg","country_code":"BJ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+230',
    '{"country":"Mauritius","flag":"https://flagcdn.com/w320/mu.jpg","country_code":"MU"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+231',
    '{"country":"Liberia","flag":"https://flagcdn.com/w320/lr.jpg","country_code":"LR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+232',
    '{"country":"Sierra Leone","flag":"https://flagcdn.com/w320/sl.jpg","country_code":"SL"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+233',
    '{"country":"Ghana","flag":"https://flagcdn.com/w320/gh.jpg","country_code":"GH"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+234',
    '{"country":"Nigeria","flag":"https://flagcdn.com/w320/ng.jpg","country_code":"NG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+235',
    '{"country":"Chad","flag":"https://flagcdn.com/w320/td.jpg","country_code":"TD"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+236',
    '{"country":"Central African Republic","flag":"https://flagcdn.com/w320/cf.jpg","country_code":"CF"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+237',
    '{"country":"Cameroon","flag":"https://flagcdn.com/w320/cm.jpg","country_code":"CM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+238',
    '{"country":"Cape Verde","flag":"https://flagcdn.com/w320/cv.jpg","country_code":"CV"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+239',
    '{"country":"São Tomé and Príncipe","flag":"https://flagcdn.com/w320/st.jpg","country_code":"ST"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+240',
    '{"country":"Equatorial Guinea","flag":"https://flagcdn.com/w320/gq.jpg","country_code":"GQ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+241',
    '{"country":"Gabon","flag":"https://flagcdn.com/w320/ga.jpg","country_code":"GA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+242',
    '{"country":"Republic of the Congo","flag":"https://flagcdn.com/w320/cg.jpg","country_code":"CG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+243',
    '{"country":"Democratic Republic of the Congo","flag":"https://flagcdn.com/w320/cd.jpg","country_code":"CD"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+244',
    '{"country":"Angola","flag":"https://flagcdn.com/w320/ao.jpg","country_code":"AO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+245',
    '{"country":"Guinea-Bissau","flag":"https://flagcdn.com/w320/gw.jpg","country_code":"GW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+248',
    '{"country":"Seychelles","flag":"https://flagcdn.com/w320/sc.jpg","country_code":"SC"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+249',
    '{"country":"Sudan","flag":"https://flagcdn.com/w320/sd.jpg","country_code":"SD"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+250',
    '{"country":"Rwanda","flag":"https://flagcdn.com/w320/rw.jpg","country_code":"RW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+251',
    '{"country":"Ethiopia","flag":"https://flagcdn.com/w320/et.jpg","country_code":"ET"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+252',
    '{"country":"Somalia","flag":"https://flagcdn.com/w320/so.jpg","country_code":"SO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+253',
    '{"country":"Djibouti","flag":"https://flagcdn.com/w320/dj.jpg","country_code":"DJ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+254',
    '{"country":"Kenya","flag":"https://flagcdn.com/w320/ke.jpg","country_code":"KE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+255',
    '{"country":"Tanzania","flag":"https://flagcdn.com/w320/tz.jpg","country_code":"TZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+256',
    '{"country":"Uganda","flag":"https://flagcdn.com/w320/ug.jpg","country_code":"UG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+257',
    '{"country":"Burundi","flag":"https://flagcdn.com/w320/bi.jpg","country_code":"BI"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+258',
    '{"country":"Mozambique","flag":"https://flagcdn.com/w320/mz.jpg","country_code":"MZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+260',
    '{"country":"Zambia","flag":"https://flagcdn.com/w320/zm.jpg","country_code":"ZM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+261',
    '{"country":"Madagascar","flag":"https://flagcdn.com/w320/mg.jpg","country_code":"MG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+262',
    '{"country":"Réunion","flag":"https://flagcdn.com/w320/re.jpg","country_code":"RE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+263',
    '{"country":"Zimbabwe","flag":"https://flagcdn.com/w320/zw.jpg","country_code":"ZW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+264',
    '{"country":"Namibia","flag":"https://flagcdn.com/w320/na.jpg","country_code":"NA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+265',
    '{"country":"Malawi","flag":"https://flagcdn.com/w320/mw.jpg","country_code":"MW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+266',
    '{"country":"Lesotho","flag":"https://flagcdn.com/w320/ls.jpg","country_code":"LS"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+267',
    '{"country":"Botswana","flag":"https://flagcdn.com/w320/bw.jpg","country_code":"BW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+268',
    '{"country":"Eswatini","flag":"https://flagcdn.com/w320/sz.jpg","country_code":"SZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+269',
    '{"country":"Comoros","flag":"https://flagcdn.com/w320/km.jpg","country_code":"KM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+27',
    '{"country":"South Africa","flag":"https://flagcdn.com/w320/za.jpg","country_code":"ZA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+290',
    '{"country":"Saint Helena","flag":"https://flagcdn.com/w320/sh.jpg","country_code":"SH"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+291',
    '{"country":"Eritrea","flag":"https://flagcdn.com/w320/er.jpg","country_code":"ER"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+297',
    '{"country":"Aruba","flag":"https://flagcdn.com/w320/aw.jpg","country_code":"AW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+298',
    '{"country":"Faroe Islands","flag":"https://flagcdn.com/w320/fo.jpg","country_code":"FO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+299',
    '{"country":"Greenland","flag":"https://flagcdn.com/w320/gl.jpg","country_code":"GL"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),

-- Zone 3 & 4 - Europe
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+30',
    '{"country":"Greece","flag":"https://flagcdn.com/w320/gr.jpg","country_code":"GR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+31',
    '{"country":"Netherlands","flag":"https://flagcdn.com/w320/nl.jpg","country_code":"NL"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+32',
    '{"country":"Belgium","flag":"https://flagcdn.com/w320/be.jpg","country_code":"BE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+33',
    '{"country":"France","flag":"https://flagcdn.com/w320/fr.jpg","country_code":"FR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+34',
    '{"country":"Spain","flag":"https://flagcdn.com/w320/es.jpg","country_code":"ES"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+350',
    '{"country":"Gibraltar","flag":"https://flagcdn.com/w320/gi.jpg","country_code":"GI"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+351',
    '{"country":"Portugal","flag":"https://flagcdn.com/w320/pt.jpg","country_code":"PT"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+352',
    '{"country":"Luxembourg","flag":"https://flagcdn.com/w320/lu.jpg","country_code":"LU"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+353',
    '{"country":"Ireland","flag":"https://flagcdn.com/w320/ie.jpg","country_code":"IE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+354',
    '{"country":"Iceland","flag":"https://flagcdn.com/w320/is.jpg","country_code":"IS"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+355',
    '{"country":"Albania","flag":"https://flagcdn.com/w320/al.jpg","country_code":"AL"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+356',
    '{"country":"Malta","flag":"https://flagcdn.com/w320/mt.jpg","country_code":"MT"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+357',
    '{"country":"Cyprus","flag":"https://flagcdn.com/w320/cy.jpg","country_code":"CY"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+358',
    '{"country":"Finland","flag":"https://flagcdn.com/w320/fi.jpg","country_code":"FI"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+359',
    '{"country":"Bulgaria","flag":"https://flagcdn.com/w320/bg.jpg","country_code":"BG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+36',
    '{"country":"Hungary","flag":"https://flagcdn.com/w320/hu.jpg","country_code":"HU"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+370',
    '{"country":"Lithuania","flag":"https://flagcdn.com/w320/lt.jpg","country_code":"LT"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+371',
    '{"country":"Latvia","flag":"https://flagcdn.com/w320/lv.jpg","country_code":"LV"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+372',
    '{"country":"Estonia","flag":"https://flagcdn.com/w320/ee.jpg","country_code":"EE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+373',
    '{"country":"Moldova","flag":"https://flagcdn.com/w320/md.jpg","country_code":"MD"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+374',
    '{"country":"Armenia","flag":"https://flagcdn.com/w320/am.jpg","country_code":"AM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+375',
    '{"country":"Belarus","flag":"https://flagcdn.com/w320/by.jpg","country_code":"BY"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+376',
    '{"country":"Andorra","flag":"https://flagcdn.com/w320/ad.jpg","country_code":"AD"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+377',
    '{"country":"Monaco","flag":"https://flagcdn.com/w320/mc.jpg","country_code":"MC"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+378',
    '{"country":"San Marino","flag":"https://flagcdn.com/w320/sm.jpg","country_code":"SM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+379',
    '{"country":"Vatican City","flag":"https://flagcdn.com/w320/va.jpg","country_code":"VA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+380',
    '{"country":"Ukraine","flag":"https://flagcdn.com/w320/ua.jpg","country_code":"UA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+381',
    '{"country":"Serbia","flag":"https://flagcdn.com/w320/rs.jpg","country_code":"RS"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+382',
    '{"country":"Montenegro","flag":"https://flagcdn.com/w320/me.jpg","country_code":"ME"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+383',
    '{"country":"Kosovo","flag":"https://flagcdn.com/w320/xk.jpg","country_code":"XK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+385',
    '{"country":"Croatia","flag":"https://flagcdn.com/w320/hr.jpg","country_code":"HR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+386',
    '{"country":"Slovenia","flag":"https://flagcdn.com/w320/si.jpg","country_code":"SI"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+387',
    '{"country":"Bosnia and Herzegovina","flag":"https://flagcdn.com/w320/ba.jpg","country_code":"BA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+389',
    '{"country":"North Macedonia","flag":"https://flagcdn.com/w320/mk.jpg","country_code":"MK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+39',
    '{"country":"Italy","flag":"https://flagcdn.com/w320/it.jpg","country_code":"IT"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+40',
    '{"country":"Romania","flag":"https://flagcdn.com/w320/ro.jpg","country_code":"RO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+41',
    '{"country":"Switzerland","flag":"https://flagcdn.com/w320/ch.jpg","country_code":"CH"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+420',
    '{"country":"Czech Republic","flag":"https://flagcdn.com/w320/cz.jpg","country_code":"CZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+421',
    '{"country":"Slovakia","flag":"https://flagcdn.com/w320/sk.jpg","country_code":"SK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+423',
    '{"country":"Liechtenstein","flag":"https://flagcdn.com/w320/li.jpg","country_code":"LI"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+43',
    '{"country":"Austria","flag":"https://flagcdn.com/w320/at.jpg","country_code":"AT"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+44',
    '{"country":"United Kingdom","flag":"https://flagcdn.com/w320/gb.jpg","country_code":"GB"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+45',
    '{"country":"Denmark","flag":"https://flagcdn.com/w320/dk.jpg","country_code":"DK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+46',
    '{"country":"Sweden","flag":"https://flagcdn.com/w320/se.jpg","country_code":"SE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+47',
    '{"country":"Norway","flag":"https://flagcdn.com/w320/no.jpg","country_code":"NO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+48',
    '{"country":"Poland","flag":"https://flagcdn.com/w320/pl.jpg","country_code":"PL"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+49',
    '{"country":"Germany","flag":"https://flagcdn.com/w320/de.jpg","country_code":"DE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),

-- Zone 5 - South and Central America
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+500',
    '{"country":"Falkland Islands","flag":"https://flagcdn.com/w320/fk.jpg","country_code":"FK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+501',
    '{"country":"Belize","flag":"https://flagcdn.com/w320/bz.jpg","country_code":"BZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+502',
    '{"country":"Guatemala","flag":"https://flagcdn.com/w320/gt.jpg","country_code":"GT"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+503',
    '{"country":"El Salvador","flag":"https://flagcdn.com/w320/sv.jpg","country_code":"SV"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+504',
    '{"country":"Honduras","flag":"https://flagcdn.com/w320/hn.jpg","country_code":"HN"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+505',
    '{"country":"Nicaragua","flag":"https://flagcdn.com/w320/ni.jpg","country_code":"NI"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+506',
    '{"country":"Costa Rica","flag":"https://flagcdn.com/w320/cr.jpg","country_code":"CR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+507',
    '{"country":"Panama","flag":"https://flagcdn.com/w320/pa.jpg","country_code":"PA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+508',
    '{"country":"Saint-Pierre and Miquelon","flag":"https://flagcdn.com/w320/pm.jpg","country_code":"PM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+509',
    '{"country":"Haiti","flag":"https://flagcdn.com/w320/ht.jpg","country_code":"HT"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+51',
    '{"country":"Peru","flag":"https://flagcdn.com/w320/pe.jpg","country_code":"PE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+52',
    '{"country":"Mexico","flag":"https://flagcdn.com/w320/mx.jpg","country_code":"MX"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+53',
    '{"country":"Cuba","flag":"https://flagcdn.com/w320/cu.jpg","country_code":"CU"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+54',
    '{"country":"Argentina","flag":"https://flagcdn.com/w320/ar.jpg","country_code":"AR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+55',
    '{"country":"Brazil","flag":"https://flagcdn.com/w320/br.jpg","country_code":"BR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+56',
    '{"country":"Chile","flag":"https://flagcdn.com/w320/cl.jpg","country_code":"CL"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+57',
    '{"country":"Colombia","flag":"https://flagcdn.com/w320/co.jpg","country_code":"CO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+58',
    '{"country":"Venezuela","flag":"https://flagcdn.com/w320/ve.jpg","country_code":"VE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+590',
    '{"country":"Guadeloupe","flag":"https://flagcdn.com/w320/gp.jpg","country_code":"GP"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+591',
    '{"country":"Bolivia","flag":"https://flagcdn.com/w320/bo.jpg","country_code":"BO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+592',
    '{"country":"Guyana","flag":"https://flagcdn.com/w320/gy.jpg","country_code":"GY"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+593',
    '{"country":"Ecuador","flag":"https://flagcdn.com/w320/ec.jpg","country_code":"EC"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+594',
    '{"country":"French Guiana","flag":"https://flagcdn.com/w320/gf.jpg","country_code":"GF"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+595',
    '{"country":"Paraguay","flag":"https://flagcdn.com/w320/py.jpg","country_code":"PY"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+596',
    '{"country":"Martinique","flag":"https://flagcdn.com/w320/mq.jpg","country_code":"MQ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+597',
    '{"country":"Suriname","flag":"https://flagcdn.com/w320/sr.jpg","country_code":"SR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+598',
    '{"country":"Uruguay","flag":"https://flagcdn.com/w320/uy.jpg","country_code":"UY"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+599',
    '{"country":"Curaçao","flag":"https://flagcdn.com/w320/cw.jpg","country_code":"CW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),

-- Zone 6 - Southeast Asia and Oceania
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+60',
    '{"country":"Malaysia","flag":"https://flagcdn.com/w320/my.jpg","country_code":"MY"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+61',
    '{"country":"Australia","flag":"https://flagcdn.com/w320/au.jpg","country_code":"AU"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+62',
    '{"country":"Indonesia","flag":"https://flagcdn.com/w320/id.jpg","country_code":"ID"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+63',
    '{"country":"Philippines","flag":"https://flagcdn.com/w320/ph.jpg","country_code":"PH"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+64',
    '{"country":"New Zealand","flag":"https://flagcdn.com/w320/nz.jpg","country_code":"NZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+65',
    '{"country":"Singapore","flag":"https://flagcdn.com/w320/sg.jpg","country_code":"SG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+66',
    '{"country":"Thailand","flag":"https://flagcdn.com/w320/th.jpg","country_code":"TH"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+670',
    '{"country":"East Timor","flag":"https://flagcdn.com/w320/tl.jpg","country_code":"TL"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+673',
    '{"country":"Brunei","flag":"https://flagcdn.com/w320/bn.jpg","country_code":"BN"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+674',
    '{"country":"Nauru","flag":"https://flagcdn.com/w320/nr.jpg","country_code":"NR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+675',
    '{"country":"Papua New Guinea","flag":"https://flagcdn.com/w320/pg.jpg","country_code":"PG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+676',
    '{"country":"Tonga","flag":"https://flagcdn.com/w320/to.jpg","country_code":"TO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+677',
    '{"country":"Solomon Islands","flag":"https://flagcdn.com/w320/sb.jpg","country_code":"SB"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+678',
    '{"country":"Vanuatu","flag":"https://flagcdn.com/w320/vu.jpg","country_code":"VU"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+679',
    '{"country":"Fiji","flag":"https://flagcdn.com/w320/fj.jpg","country_code":"FJ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+680',
    '{"country":"Palau","flag":"https://flagcdn.com/w320/pw.jpg","country_code":"PW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+681',
    '{"country":"Wallis and Futuna","flag":"https://flagcdn.com/w320/wf.jpg","country_code":"WF"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+682',
    '{"country":"Cook Islands","flag":"https://flagcdn.com/w320/ck.jpg","country_code":"CK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+683',
    '{"country":"Niue","flag":"https://flagcdn.com/w320/nu.jpg","country_code":"NU"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+685',
    '{"country":"Samoa","flag":"https://flagcdn.com/w320/ws.jpg","country_code":"WS"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+686',
    '{"country":"Kiribati","flag":"https://flagcdn.com/w320/ki.jpg","country_code":"KI"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+687',
    '{"country":"New Caledonia","flag":"https://flagcdn.com/w320/nc.jpg","country_code":"NC"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+688',
    '{"country":"Tuvalu","flag":"https://flagcdn.com/w320/tv.jpg","country_code":"TV"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+689',
    '{"country":"French Polynesia","flag":"https://flagcdn.com/w320/pf.jpg","country_code":"PF"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+690',
    '{"country":"Tokelau","flag":"https://flagcdn.com/w320/tk.jpg","country_code":"TK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+691',
    '{"country":"Federated States of Micronesia","flag":"https://flagcdn.com/w320/fm.jpg","country_code":"FM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+692',
    '{"country":"Marshall Islands","flag":"https://flagcdn.com/w320/mh.jpg","country_code":"MH"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),

-- Zone 7 - Russia and Kazakhstan
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+7',
    '{"country":"Russia","flag":"https://flagcdn.com/w320/ru.jpg","country_code":"RU"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+7',
    '{"country":"Kazakhstan","flag":"https://flagcdn.com/w320/kz.jpg","country_code":"KZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),

-- Zone 8 - East Asia and South Asia
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+81',
    '{"country":"Japan","flag":"https://flagcdn.com/w320/jp.jpg","country_code":"JP"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+82',
    '{"country":"South Korea","flag":"https://flagcdn.com/w320/kr.jpg","country_code":"KR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+84',
    '{"country":"Vietnam","flag":"https://flagcdn.com/w320/vn.jpg","country_code":"VN"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+850',
    '{"country":"North Korea","flag":"https://flagcdn.com/w320/kp.jpg","country_code":"KP"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+852',
    '{"country":"Hong Kong","flag":"https://flagcdn.com/w320/hk.jpg","country_code":"HK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+853',
    '{"country":"Macau","flag":"https://flagcdn.com/w320/mo.jpg","country_code":"MO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+855',
    '{"country":"Cambodia","flag":"https://flagcdn.com/w320/kh.jpg","country_code":"KH"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+856',
    '{"country":"Laos","flag":"https://flagcdn.com/w320/la.jpg","country_code":"LA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+86',
    '{"country":"China","flag":"https://flagcdn.com/w320/cn.jpg","country_code":"CN"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+880',
    '{"country":"Bangladesh","flag":"https://flagcdn.com/w320/bd.jpg","country_code":"BD"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+886',
    '{"country":"Taiwan","flag":"https://flagcdn.com/w320/tw.jpg","country_code":"TW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),

-- Zone 9 - Middle East, West Asia, Central Asia
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+90',
    '{"country":"Turkey","flag":"https://flagcdn.com/w320/tr.jpg","country_code":"TR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+91',
    '{"country":"India","flag":"https://flagcdn.com/w320/in.jpg","country_code":"IN"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+92',
    '{"country":"Pakistan","flag":"https://flagcdn.com/w320/pk.jpg","country_code":"PK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+93',
    '{"country":"Afghanistan","flag":"https://flagcdn.com/w320/af.jpg","country_code":"AF"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+94',
    '{"country":"Sri Lanka","flag":"https://flagcdn.com/w320/lk.jpg","country_code":"LK"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+95',
    '{"country":"Myanmar","flag":"https://flagcdn.com/w320/mm.jpg","country_code":"MM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+960',
    '{"country":"Maldives","flag":"https://flagcdn.com/w320/mv.jpg","country_code":"MV"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+961',
    '{"country":"Lebanon","flag":"https://flagcdn.com/w320/lb.jpg","country_code":"LB"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+962',
    '{"country":"Jordan","flag":"https://flagcdn.com/w320/jo.jpg","country_code":"JO"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+963',
    '{"country":"Syria","flag":"https://flagcdn.com/w320/sy.jpg","country_code":"SY"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+964',
    '{"country":"Iraq","flag":"https://flagcdn.com/w320/iq.jpg","country_code":"IQ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+965',
    '{"country":"Kuwait","flag":"https://flagcdn.com/w320/kw.jpg","country_code":"KW"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+966',
    '{"country":"Saudi Arabia","flag":"https://flagcdn.com/w320/sa.jpg","country_code":"SA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+967',
    '{"country":"Yemen","flag":"https://flagcdn.com/w320/ye.jpg","country_code":"YE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+968',
    '{"country":"Oman","flag":"https://flagcdn.com/w320/om.jpg","country_code":"OM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+970',
    '{"country":"Palestine","flag":"https://flagcdn.com/w320/ps.jpg","country_code":"PS"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+971',
    '{"country":"United Arab Emirates","flag":"https://flagcdn.com/w320/ae.jpg","country_code":"AE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+972',
    '{"country":"Israel","flag":"https://flagcdn.com/w320/il.jpg","country_code":"IL"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+973',
    '{"country":"Bahrain","flag":"https://flagcdn.com/w320/bh.jpg","country_code":"BH"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+974',
    '{"country":"Qatar","flag":"https://flagcdn.com/w320/qa.jpg","country_code":"QA"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+975',
    '{"country":"Bhutan","flag":"https://flagcdn.com/w320/bt.jpg","country_code":"BT"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+976',
    '{"country":"Mongolia","flag":"https://flagcdn.com/w320/mn.jpg","country_code":"MN"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+977',
    '{"country":"Nepal","flag":"https://flagcdn.com/w320/np.jpg","country_code":"NP"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+98',
    '{"country":"Iran","flag":"https://flagcdn.com/w320/ir.jpg","country_code":"IR"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+992',
    '{"country":"Tajikistan","flag":"https://flagcdn.com/w320/tj.jpg","country_code":"TJ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+993',
    '{"country":"Turkmenistan","flag":"https://flagcdn.com/w320/tm.jpg","country_code":"TM"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+994',
    '{"country":"Azerbaijan","flag":"https://flagcdn.com/w320/az.jpg","country_code":"AZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+995',
    '{"country":"Georgia","flag":"https://flagcdn.com/w320/ge.jpg","country_code":"GE"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+996',
    '{"country":"Kyrgyzstan","flag":"https://flagcdn.com/w320/kg.jpg","country_code":"KG"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+998',
    '{"country":"Uzbekistan","flag":"https://flagcdn.com/w320/uz.jpg","country_code":"UZ"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),

-- Caribbean countries with +1 area codes (North American Numbering Plan)
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Bahamas","flag":"https://flagcdn.com/w320/bs.jpg","country_code":"BS","area_code":"242"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Barbados","flag":"https://flagcdn.com/w320/bb.jpg","country_code":"BB","area_code":"246"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Anguilla","flag":"https://flagcdn.com/w320/ai.jpg","country_code":"AI","area_code":"264"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Antigua and Barbuda","flag":"https://flagcdn.com/w320/ag.jpg","country_code":"AG","area_code":"268"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"British Virgin Islands","flag":"https://flagcdn.com/w320/vg.jpg","country_code":"VG","area_code":"284"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Cayman Islands","flag":"https://flagcdn.com/w320/ky.jpg","country_code":"KY","area_code":"345"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Bermuda","flag":"https://flagcdn.com/w320/bm.jpg","country_code":"BM","area_code":"441"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Grenada","flag":"https://flagcdn.com/w320/gd.jpg","country_code":"GD","area_code":"473"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Turks and Caicos Islands","flag":"https://flagcdn.com/w320/tc.jpg","country_code":"TC","area_code":"649"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Montserrat","flag":"https://flagcdn.com/w320/ms.jpg","country_code":"MS","area_code":"664"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Sint Maarten","flag":"https://flagcdn.com/w320/sx.jpg","country_code":"SX","area_code":"721"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Saint Lucia","flag":"https://flagcdn.com/w320/lc.jpg","country_code":"LC","area_code":"758"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Dominica","flag":"https://flagcdn.com/w320/dm.jpg","country_code":"DM","area_code":"767"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Saint Vincent and the Grenadines","flag":"https://flagcdn.com/w320/vc.jpg","country_code":"VC","area_code":"784"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Dominican Republic","flag":"https://flagcdn.com/w320/do.jpg","country_code":"DO","area_code":"809"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Trinidad and Tobago","flag":"https://flagcdn.com/w320/tt.jpg","country_code":"TT","area_code":"868"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Saint Kitts and Nevis","flag":"https://flagcdn.com/w320/kn.jpg","country_code":"KN","area_code":"869"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),'UM_4', 'phone_codes', '+1',
    '{"country":"Jamaica","flag":"https://flagcdn.com/w320/jm.jpg","country_code":"JM","area_code":"876"}',
    (SELECT "Id" FROM ekkomd_core.tbl_universal_picklist WHERE "master_code" = 'UM_4'),
    CURRENT_TIMESTAMP
);