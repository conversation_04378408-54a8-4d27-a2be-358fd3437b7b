import { Controller, Get, Req } from '@nestjs/common';
import { Request } from 'express';
import { OnboardingService } from './onboarding.service';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';

@Controller('onboarding')
export class OnboardingController {
  constructor(private readonly onboardingService: OnboardingService) {}

  @Get('authorization')
  async verifyDoctor(@GetUserEmail() userEmail: string) {
    return await this.onboardingService.verifyDoctorOnboarding(userEmail);
  }
}
