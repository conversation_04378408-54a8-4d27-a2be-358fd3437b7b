export const apiVersion = 'SCRIB-621';

export enum LLM_RESPONSETYPES {
  JSON,
  TEXT,
  HTML,
}

export const responseTypes = {
  Labs: 'LABS',
  Notes: 'NOTES',
  Prescriptions: 'PRESCRIPTIONS',
  Vitals: 'VITALS',
};

export const STATUS = {
  PENDING: 'PENDING',
  REJECTED: 'REJECTED',
  ACCEPTED: 'ACCEPTED',
};

export const VERIFICATION_STATUS = {
  INCOMPLETE_PROFILE: 'INCOMPLETE_PROFILE',
  SUBSCRIPTION_ACTIVE: 'SUBSCRIPTION_ACTIVE',
  SUBSCRIPTION_EXPIRED: 'SUBSCRIPTION_EXPIRED',
  NO_SUBSCRIPTION: 'NO_SUBSCRIPTION',
  ADMIN: 'ADMIN',
  NO_ACTIVE_TENANT: 'NO_ACTIVE_TENANT',
};

export const TRANSCRIPTION_TYPES = {
  ORIGINAL: 'ORIGINAL',
  APPENDED: 'APPENDED',
};

export const CUSTOM_LETTERHEAD_SECTIONS = {
  DOCTOR_NAME: 'fa959dbb-7a24-47a5-bef0-1d442c4c0865',
  DOCTOR_PHONE: 'f3a0453b-3a86-4dcc-ae32-16d0a0635194',
  DOCTOR_EMAIL: '3b13f419-a890-401b-bc7d-6ab6790185d9',
};

export const DECORATOR_KEYS = {
  PERMISSIONS: 'permissions',
  IS_PUBLIC: 'isPublic',
};
