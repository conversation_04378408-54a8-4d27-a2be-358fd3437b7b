import { Injectable, Logger } from '@nestjs/common';
import { FeedbackRepository } from './feedback.repository';
import { CreateFeedbackDto, FeedbackEvidenceDto } from './dto/feedback.dto';
import { UtilsService } from 'src/common/utils/utils.service';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';

@Injectable()
export class FeedbackService {
  private readonly logger = new Logger(FeedbackService.name);
  constructor(
    private readonly feedbackRepository: FeedbackRepository,
    private readonly utilsService: UtilsService,
    private readonly s3FileUploadService: s3FileUploadService,
  ) {}

  async createFeedback(
    userEmail: string,
    encounterId: string,
    data: CreateFeedbackDto,
  ) {
    return await this.feedbackRepository.createFeedback(
      userEmail,
      encounterId,
      data,
    );
  }
  async createFeedbackEvidence(
    userEmail: string,
    feedbackId: string,
    model: FeedbackEvidenceDto,
  ) {
    return await this.feedbackRepository.createFeedbackEvidence(
      userEmail,
      feedbackId,
      model,
    );
  }

  async uploadFeedbackScreenshot(file: Express.Multer.File, feedbackId: string): Promise<string> {
    //try to upload the file to s3 and return the url
    try {
      const url = await this.s3FileUploadService.uploadFileToS3(
        file,
        `${feedbackId}/feedback-screenshot/${new Date().toISOString()}`,
      );
      return url;
    } catch (error) {
      this.logger.error(`Error uploading feedback screenshot: ${error.stack}`);
      throw error;
    }
  }
}
