/*
  Warnings:

  - Made the column `module_name` on table `tbl_identifier_lk` required. This step will fail if there are existing NULL values in that column.
  - Made the column `fe_identifier` on table `tbl_identifier_lk` required. This step will fail if there are existing NULL values in that column.
  - Made the column `be_identifier` on table `tbl_identifier_lk` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "tbl_identifier_lk" ADD COLUMN     "identifier_type" TEXT,
ALTER COLUMN "module_name" SET NOT NULL,
ALTER COLUMN "fe_identifier" SET NOT NULL,
ALTER COLUMN "fe_identifier" SET DATA TYPE JSONB USING fe_identifier::jsonb,
ALTER COLUMN "be_identifier" SET NOT NULL,
ALTER COLUMN "be_identifier" SET DATA TYPE JSONB USING be_identifier::jsonb;

-- CreateTable
CREATE TABLE "tbl_object_type_value" (
    "object_type_value_id" UUID NOT NULL,
    "type_name" TEXT NOT NULL,
    "type_value" TEXT NOT NULL,
    "object_value_type" TEXT,
    "object_type_id" TEXT NOT NULL,
    "identifier_id" UUID NOT NULL,
    "tenant_id" UUID,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_object_type_value_pkey" PRIMARY KEY ("object_type_value_id")
);

-- CreateTable
CREATE TABLE "tbl_permission_lk" (
    "permission_id" UUID NOT NULL,
    "object_type_value_id" UUID NOT NULL,
    "view_enable" BOOLEAN NOT NULL,
    "create" BOOLEAN NOT NULL,
    "read" BOOLEAN NOT NULL,
    "update" BOOLEAN NOT NULL,
    "delete" BOOLEAN NOT NULL,
    "tenant_id" UUID,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_permission_lk_pkey" PRIMARY KEY ("permission_id")
);

-- CreateIndex
CREATE INDEX "tbl_object_type_value_identifier_id_idx" ON "tbl_object_type_value"("identifier_id");

-- CreateIndex
CREATE INDEX "tbl_permission_lk_object_type_value_id_idx" ON "tbl_permission_lk"("object_type_value_id");

-- AddForeignKey
ALTER TABLE "tbl_object_type_value" ADD CONSTRAINT "tbl_object_type_value_type_name_fkey" FOREIGN KEY ("type_name") REFERENCES "tbl_object_type_lk"("object_type_name") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_object_type_value" ADD CONSTRAINT "tbl_object_type_value_identifier_id_fkey" FOREIGN KEY ("identifier_id") REFERENCES "tbl_identifier_lk"("identifier_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_permission_lk" ADD CONSTRAINT "tbl_permission_lk_object_type_value_id_fkey" FOREIGN KEY ("object_type_value_id") REFERENCES "tbl_object_type_value"("object_type_value_id") ON DELETE RESTRICT ON UPDATE CASCADE;
