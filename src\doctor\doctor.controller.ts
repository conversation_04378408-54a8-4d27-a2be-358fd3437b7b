import { Body, Controller, Get, Post, Req, Res } from '@nestjs/common';
import { Response, Request } from 'express';
import { PrismaService } from '../common/prisma/prisma.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UpsertDoctorDto } from './dtos/DoctorDetails.dto';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { ApiResponseDTO } from 'src/common/dtos';
import { Doctor, Specialties } from '@prisma/client';
import { DoctorService } from './doctor.service';
import { PERMISSIONS } from 'src/common/decorators/permissions.decorator';

@ApiTags('Doctors')
@ApiBearerAuth('access-token')
@Controller('doctors')
export class DoctorController {
  constructor(
    private readonly prisma: PrismaService,
    private readonly doctorservie: DoctorService,
  ) {}

  @Get()
  @PERMISSIONS(
    'doctor.read.self',
    'doctor.read.org',
    'doctor.create.self',
    'doctor.create.org',
  )
  async getOrCreateDoctor(@Req() req: Request, @Res() res: Response) {
    const email = (req.user as any).email;
    if (!email) {
      return res
        .status(400)
        .send({ message: 'Email claim not found in token' });
    }

    let doctor = await this.prisma.doctor.findUnique({
      where: { email },
    });

    if (!doctor) {
      // Create new doctor record. You may want to extract first/last name from other claims or ask the user after.
      doctor = await this.prisma.doctor.create({
        data: {
          email,
          first_name: 'Unknown',
          last_name: 'Unknown',
        },
      });
    }

    return res.status(200).send({
      statusCode: 200,
      message: 'Doctor record fetched/created successfully',
      data: {
        doctorId: doctor.doctor_id,
        email: doctor.email,
        firstName: doctor.first_name,
        lastName: doctor.last_name,
      },
    });
  }

  @Post('save-data')
  @PERMISSIONS(
    'doctor.update.self',
    'doctor.update.org',
    'doctor.create.self',
    'doctor.create.org',
  )
  async upsertDoctor(
    @Body() model: UpsertDoctorDto,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Doctor>> {
    return { data: await this.doctorservie.upsertDoctor(model, userEmail) };
  }

  @Get('specialties')
  async getDoctorSpecialities(): Promise<ApiResponseDTO<Specialties[]>> {
    return {
      data: await this.doctorservie.getAllSpecialities(),
    };
  }

  @Get('doctor/details')
  @PERMISSIONS('doctor.read.self', 'doctor.read.org')
  async getDoctorDetails(@GetUserEmail() userEamil: string) {
    return await this.doctorservie.getDoctorDetailsByEmail(userEamil);
  }
}
