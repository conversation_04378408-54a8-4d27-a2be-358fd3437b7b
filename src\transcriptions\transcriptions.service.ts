import { Injectable } from '@nestjs/common';
import { AudioTranscription } from '@prisma/client';
import { TranscriptionsRepository } from './transcriptions.repository';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { EncounterRepository } from 'src/encounter/encounter.repository';

@Injectable()
export class TranscriptionsService {
  constructor(
    private readonly transcriptionrepository: TranscriptionsRepository,
    private readonly prismaservice: PrismaService,
    private readonly encounterRepository: EncounterRepository,
  ) {}
  async getTranscriptionUsingEncounterId(
    encounter_id: string,
  ): Promise<AudioTranscription[] | any> {
    const transcriptiondata =
      await this.transcriptionrepository.getTranscriptionUsingEncounterId(
        encounter_id,
      );

    const EncounterDetails = await this.prismaservice.encounter.findFirst({
      where: {
        encounter_id: encounter_id,
        isActive: true,
      },
      select: {
        encounter_status: true,
        NoteHeader: {
          select: {
            note_details: {
              select: {
                note_detail_id: true,
              },
            },
          },
        },
        labs: {
          select: {
            lab_id: true,
          },
        },
        Prescriptions: {
          select: {
            prescription_id: true,
          },
        },
      },
    });

    if (EncounterDetails?.encounter_status == 'IN_PROGRESS') {
      if (transcriptiondata.length > 0) {
        if (
          !EncounterDetails.NoteHeader[0]?.note_details ||
          EncounterDetails.NoteHeader[0]?.note_details?.length == 0 ||
          !EncounterDetails.labs ||
          EncounterDetails.labs?.length == 0 ||
          !EncounterDetails.Prescriptions ||
          EncounterDetails.Prescriptions?.length == 0
        ) {
          await this.encounterRepository.updateEncounterStatus(
            encounter_id,
            'PENDING_SIGN_OFF',
          );
        }
      }
    }

    return {
      AudioTranscription: transcriptiondata,
      NOTE_GENERATED_FLAG:
        !EncounterDetails.NoteHeader[0]?.note_details ||
        EncounterDetails.NoteHeader[0]?.note_details?.length == 0
          ? false
          : true,
      LABS_GENERATED_FLAG:
        !EncounterDetails.labs || EncounterDetails.labs?.length == 0
          ? false
          : true,
      PRESCRIPTION_GENERATED_FLAG:
        !EncounterDetails.Prescriptions ||
        EncounterDetails.Prescriptions?.length == 0
          ? false
          : true,
    };
  }
}
