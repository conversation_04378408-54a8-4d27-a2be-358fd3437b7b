import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class CreateLabDto {
  @Type(() => LabDetailsDto)
  @ValidateNested({ each: true })
  info: LabDetailsDto[];
}

export class LabDetailsDto {
  @ApiProperty({
    description: 'The name of the lab test',
    example: ' 6fd6be04-e7e3-448b-8dc3-dbc6636563a3',
  })
  @IsNotEmpty({ message: 'Encounter id is Required' })
  @IsString({ message: 'Encounter id must be a string' })
  encounter_id: string;

  @ApiProperty({
    description: 'The name of the lab test',
    example: 'CBC',
  })
  @IsNotEmpty({ message: 'Lab name is Required' })
  lab_name: string;

  @ApiProperty({
    description: 'The notes for the lab test',
    example: 'this is a note',
  })
  @IsOptional()
  notes: string;

  @ApiProperty({
    description: 'The reason of the lab test',
    example: 'this is a reason',
  })
  @IsOptional()
  reason: string;

  @ApiProperty({
    description: 'status of the lab',
    example: 'PENDING | ACCEPTED | REJECTED',
  })
  @IsNotEmpty()
  @IsString()
  status: string;

  @ApiProperty({
    description: 'created by',
    example: '',
  })
  @IsOptional()
  created_by: string;
}

export class UpdateLabDto {
  @ApiProperty({
    description: 'The name of the lab test',
    example: 'CBC',
  })
  @IsNotEmpty({ message: 'Lab name is Required' })
  lab_name: string;

  @ApiProperty({
    description: 'The notes for the lab test',
    example: 'this is a note',
  })
  @IsOptional()
  notes: string;

  @ApiProperty({
    description: 'The reason of the lab test',
    example: 'this is a reason',
  })
  @IsOptional()
  reason: string;
}
