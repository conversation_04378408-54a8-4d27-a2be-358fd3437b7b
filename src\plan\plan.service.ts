import { Injectable } from '@nestjs/common';
import { Plan } from '@prisma/client';
import { PaginatedResultDTO } from 'src/common/dtos';
import { PlanRepository } from './plan.repository';

@Injectable()
export class PlanService {
  constructor(private readonly planrepository: PlanRepository) {}
  async getPlans(
    page: number=0,
    limit: number=10,
  ): Promise<PaginatedResultDTO<Plan>> {
    return await this.planrepository.getAllPlans(page, limit);
  }
}
