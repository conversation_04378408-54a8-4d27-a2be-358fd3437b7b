import { Module } from '@nestjs/common';
import { UniversalPicklistService } from './universal-picklist.service';
import { UniversalPicklistController } from './universal-picklist.controller';
import { UniversalPicklistRepository } from './universal-picklist.repository';

@Module({
  controllers: [UniversalPicklistController],
  providers: [UniversalPicklistService,UniversalPicklistRepository],
})
export class UniversalPicklistModule {}
