-- CreateTable
CREATE TABLE "tbl_user_details" (
    "user_detail_id" UUID NOT NULL,
    "user_name" TEXT NOT NULL,
    "first_name" TEXT,
    "last_name" TEXT,
    "email" TEXT NOT NULL,
    "phone_number" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_user_details_pkey" PRIMARY KEY ("user_detail_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tbl_user_details_email_key" ON "tbl_user_details"("email");
