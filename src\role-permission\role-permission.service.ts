import { Injectable } from '@nestjs/common';
import { RolePermissionRepository } from './role-permission.repository';
import { CreateRolePermissionMappingDto } from './dtos/create-role-permission-mapping.dto';
import { tbl_role_permission_mapping } from '@prisma/client';
import { PaginatedResultDTO } from 'src/common/dtos';

@Injectable()
export class RolePermissionService {
  constructor(private readonly repo: RolePermissionRepository) {}

  async createMapping(dto: CreateRolePermissionMappingDto, userEmail: string): Promise<tbl_role_permission_mapping> {
    return await this.repo.createMapping(dto, userEmail);
  }

  async findMappings(
    role_id?: string,
    permission_id?: string,
    page = 1,
    limit = 10,
  ): Promise<PaginatedResultDTO<tbl_role_permission_mapping>> {
    return await this.repo.findMappings(role_id, permission_id, page, limit);
  }

  async findOne(id: string): Promise<tbl_role_permission_mapping> {
    return await this.repo.findMappingById(id);
  }

  async updateMapping(id: string, dto: Partial<CreateRolePermissionMappingDto>, userEmail: string): Promise<tbl_role_permission_mapping> {
    return await this.repo.updateMapping(id, dto, userEmail);
  }

  async deleteMapping(id: string, userEmail: string): Promise<tbl_role_permission_mapping> {
    return await this.repo.deleteMapping(id, userEmail);
  }

    async getRolePermissions(){
    return await this.repo.getRolesPermissions();
  }
}
