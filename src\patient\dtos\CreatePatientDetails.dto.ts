import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Gender } from '@prisma/client';
import {
  IsAlpha,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class CreatePatientDetailsDto {
  @ApiProperty({
    description: 'First name of the patient',
    example: 'Alice',
  })
  @IsNotEmpty({ message: 'Patient first name is required' })
  @Matches(/^[A-Za-z]+(?:\s+[A-Za-z]+)*$/, {
    message:
      'Patient first name must contain letters and may include spaces between words',
  })
  patientFirstName: string;

  @ApiPropertyOptional({
    description: 'Last name of the patient',
    example: '<PERSON>',
  })
  // @Matches(/^[A-Za-z]+(?:\s+[A-Za-z]+)*$/, {
  //   message:
  //     'Patient last name must contain letters and may include spaces between words',
  // })
  patientLastName?: string;

  @ApiProperty({
    description: 'Gender of the patient',
    enum: Gender,
    example: Gender.FEMALE,
  })
  @IsNotEmpty({ message: 'Patient gender is required' })
  @IsEnum(Gender, {
    message: `Patient gender must be one of: ${Object.values(Gender).join(', ')}`,
  })
  patientGender: Gender;

  @ApiProperty({
    description: 'Date of birth of the patient in ISO 8601 format',
    example: '1990-01-01',
    type: String,
    format: 'date',
  })
  @IsNotEmpty({ message: 'Patient date of birth is required' })
  @IsDateString(
    {},
    { message: 'Patient date of birth must be a valid ISO 8601 date string' },
  )
  patientDOB: string;

  @ApiPropertyOptional({
    description: "Patient's phone number in E.164 format",
    example: '+919876543211',
  })
  @IsPhoneNumber('IN', {
    message: 'Patient phone must be a valid phone number',
  })
  @MaxLength(20, {
    message: 'Patient phone number must not exceed 20 characters',
  })
  patientPhone?: string;

  @ApiPropertyOptional({
    description: "Country code of the patient's phone number",
    example: '+91',
    maxLength: 5,
  })
  @IsString({ message: 'Phone country code must be a string' })
  @MaxLength(5, { message: 'Phone country code must not exceed 5 characters' })
  phoneCountryCode?: string;

  @ApiPropertyOptional({ description: 'Address', example: '123 MG Road' })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({ description: 'District', example: 'Bangalore Urban' })
  @IsOptional()
  @IsString()
  district_name?: string;

  @ApiPropertyOptional({ description: 'State', example: 'Karnataka' })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({ description: 'PIN code', example: '560001' })
  @IsOptional()
  @IsString()
  pin?: string;

  @ApiPropertyOptional({ description: 'Country', example: 'India' })
  @IsOptional()
  @IsString()
  country?: string;

}

export class CreateQuickEncounterPatientDto{
   @ApiProperty({
    description: 'First name of the patient',
    example: 'Alice',
  })
  @IsOptional()
  @Matches(/^[A-Za-z]+(?:\s+[A-Za-z]+)*$/, {
    message:
      'Patient first name must contain letters and may include spaces between words',
  })
  patientFirstName?: string;

  @ApiPropertyOptional({
    description: 'Last name of the patient',
    example: 'Johnson',
  })
  @IsOptional()
  @Matches(/^[A-Za-z]+(?:\s+[A-Za-z]+)*$/, {
    message:
      'Patient last name must contain letters and may include spaces between words',
  })
  patientLastName?: string;

  @ApiProperty({
    description: 'Gender of the patient',
    enum: Gender,
    example: Gender.FEMALE,
  })
  @IsNotEmpty({ message: 'Patient gender is required' })
  @IsEnum(Gender, {
    message: `Patient gender must be one of: ${Object.values(Gender).join(', ')}`,
  })
  patientGender: Gender;

  @ApiProperty({
    description: 'Date of birth of the patient in ISO 8601 format',
    example: '1990-01-01',
    type: String,
    format: 'date',
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'Patient date of birth must be a valid ISO 8601 date string' },
  )
  patientDOB?: string;

  @ApiPropertyOptional({
    description: "Patient's phone number in E.164 format",
    example: '+919876543211',
  })
  @IsOptional()
  @IsPhoneNumber('IN', {
    message: 'Patient phone must be a valid phone number',
  })
  @MaxLength(20, {
    message: 'Patient phone number must not exceed 20 characters',
  })
  patientPhone?: string;

  @ApiPropertyOptional({
    description: "Country code of the patient's phone number",
    example: '+91',
    maxLength: 5,
  })
  @IsOptional()
  @IsString({ message: 'Phone country code must be a string' })
  @MaxLength(5, { message: 'Phone country code must not exceed 5 characters' })
  phoneCountryCode?: string;

  
}
