/*
  Warnings:

  - Added the required column `doctor_id` to the `encounter` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "encounter" ADD COLUMN     "doctor_id" UUID NOT NULL;

-- CreateTable
CREATE TABLE "doctor" (
    "doctor_id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by" TEXT NOT NULL DEFAULT 'ADMIN',

    CONSTRAINT "doctor_pkey" PRIMARY KEY ("doctor_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "doctor_email_key" ON "doctor"("email");

-- AddForeignKey
ALTER TABLE "encounter" ADD CONSTRAINT "encounter_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "doctor"("doctor_id") ON DELETE RESTRICT ON UPDATE CASCADE;
