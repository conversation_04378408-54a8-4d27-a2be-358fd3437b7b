/*
  Warnings:

  - The primary key for the `tbl_role_lk` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - Changed the type of `role_id` on the `tbl_role_lk` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- AlterTable
ALTER TABLE "tbl_role_lk" DROP CONSTRAINT "tbl_role_lk_pkey",
DROP COLUMN "role_id",
ADD COLUMN     "role_id" UUID NOT NULL,
ADD CONSTRAINT "tbl_role_lk_pkey" PRIMARY KEY ("role_id");

-- CreateTable
CREATE TABLE "tbl_user_role_mapping" (
    "user_role_map_id" UUID NOT NULL,
    "user_detail_id" UUID NOT NULL,
    "role_lk_id" UUID NOT NULL,
    "tenant_id" UUID,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_user_role_mapping_pkey" PRIMARY KEY ("user_role_map_id")
);

-- CreateIndex
CREATE INDEX "tbl_user_role_mapping_user_detail_id_idx" ON "tbl_user_role_mapping"("user_detail_id");

-- CreateIndex
CREATE INDEX "tbl_user_role_mapping_role_lk_id_idx" ON "tbl_user_role_mapping"("role_lk_id");

-- CreateIndex
CREATE UNIQUE INDEX "tbl_user_role_mapping_user_detail_id_role_lk_id_key" ON "tbl_user_role_mapping"("user_detail_id", "role_lk_id");

-- AddForeignKey
ALTER TABLE "tbl_user_role_mapping" ADD CONSTRAINT "tbl_user_role_mapping_role_lk_id_fkey" FOREIGN KEY ("role_lk_id") REFERENCES "tbl_role_lk"("role_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_user_role_mapping" ADD CONSTRAINT "tbl_user_role_mapping_user_detail_id_fkey" FOREIGN KEY ("user_detail_id") REFERENCES "tbl_user_details"("user_detail_id") ON DELETE RESTRICT ON UPDATE CASCADE;
