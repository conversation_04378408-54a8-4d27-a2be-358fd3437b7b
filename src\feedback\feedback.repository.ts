import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';
import { FeedbackEvidenceDto } from './dto/feedback.dto';

@Injectable()
export class FeedbackRepository {
  private readonly logger = new Logger(FeedbackRepository.name);
  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async createFeedback(userEmail: string, encounterId: string, data: any) {
    try {
      const feedback = await this.prisma.encounterFeedback.create({
        data: {
          encounter_id: encounterId,
          feedback: data.feedback,
          created_by: userEmail,
        },
      });
      return feedback;
    } catch (error) {
      this.logger.error(`Error creating feedback: ${error.stack}`);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create feedback',
        500,
      );
    }
  }

  async createFeedbackEvidence(
    userEmail: string,
    feedbackId: string,
    model: FeedbackEvidenceDto,
  ) {
    try {
      const feedbackEvidence = await this.prisma.feedbackEvidence.create({
        data: {
          feedbackId: feedbackId,
          url: model.url,
          screenshot_url: model.screenshot_url
            ? model.screenshot_url
            : undefined,
          created_by: userEmail,
        },
      });
      return feedbackEvidence;
    } catch (error) {
      this.logger.error(`Error creating feedback evidence: ${error.stack}`);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create feedback evidence',
        500,
      );
    }
  }
}
