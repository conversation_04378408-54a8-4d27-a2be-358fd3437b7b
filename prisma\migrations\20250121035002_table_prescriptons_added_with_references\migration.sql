-- CreateTable
CREATE TABLE "prescriptions" (
    "prescription_id" UUID NOT NULL,
    "drug_name" TEXT,
    "dose" TEXT,
    "sig" TEXT,
    "brand" TEXT,
    "patiendt_notes" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "encounter_id" UUID,
    "status" TEXT,
    "status_reason" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "prescriptions_pkey" PRIMARY KEY ("prescription_id")
);

-- CreateIndex
CREATE INDEX "idx_prescriptions_encounter" ON "prescriptions"("encounter_id");

-- AddForeignKey
ALTER TABLE "prescriptions" ADD CONSTRAINT "prescriptions_encounter_id_fkey" FOREIGN KEY ("encounter_id") REFERENCES "encounter"("encounter_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prescriptions" ADD CONSTRAINT "prescriptions_status_fkey" FOREIGN KEY ("status") REFERENCES "status"("status") ON DELETE SET NULL ON UPDATE CASCADE;
