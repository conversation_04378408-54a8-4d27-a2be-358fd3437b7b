-- CreateTable
CREATE TABLE "specialties" (
    "name" TEXT NOT NULL,
    "label" TEXT,
    "description" TEXT,
    "order" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "specialties_pkey" PRIMARY KEY ("name")
);

INSERT INTO specialties (name, label, "order")
VALUES 
    ('addiction_medicine', 'Addiction Medicine', 1),
    ('allergy_immunology', 'Allergy & Immunology', 2),
    ('anatomic_pathology', 'Anatomic Pathology', 3),
    ('anesthesiology', 'Anesthesiology', 4),
    ('cardiology', 'Cardiology', 5),
    ('cardiothoracic_surgery', 'Cardiothoracic Surgery', 6),
    ('child_adolescent_psychiatry', 'Child & Adolescent Psychiatry', 7),
    ('colorectal_surgery', 'Colorectal Surgery', 8),
    ('critical_care_medicine', 'Critical Care Medicine', 9),
    ('dermatology', 'Dermatology', 10),
    ('diagnostic_radiology', 'Diagnostic Radiology', 11),
    ('emergency_medicine', 'Emergency Medicine', 12),
    ('endocrinology', 'Endocrinology', 13),
    ('family_medicine', 'Family Medicine', 14),
    ('forensic_pathology', 'Forensic Pathology', 15),
    ('gastroenterology', 'Gastroenterology', 16),
    ('general_psychiatry', 'General Psychiatry', 17),
    ('general_surgery', 'General Surgery', 18),
    ('geriatric_psychiatry', 'Geriatric Psychiatry', 19),
    ('geriatrics', 'Geriatrics', 20),
    ('hematology', 'Hematology', 21),
    ('infectious_disease', 'Infectious Disease', 22),
    ('internal_medicine', 'Internal Medicine', 23),
    ('interventional_radiology', 'Interventional Radiology', 24),
    ('medical_oncology', 'Medical Oncology', 25),
    ('nephrology', 'Nephrology', 26),
    ('neurology', 'Neurology', 27),
    ('neurosurgery', 'Neurosurgery', 28),
    ('nuclear_medicine', 'Nuclear Medicine', 29),
    ('obstetrics_gynecology', 'Obstetrics & Gynecology (OB/GYN)', 30),
    ('occupational_medicine', 'Occupational Medicine', 31),
    ('ophthalmology', 'Ophthalmology', 32),
    ('orthopedic_surgery', 'Orthopedic Surgery', 33),
    ('otolaryngology', 'Otolaryngology (ENT)', 34),
    ('pain_medicine', 'Pain Medicine', 35),
    ('palliative_care_hospice', 'Palliative Care & Hospice Medicine', 36),
    ('pediatrics', 'Pediatrics', 37),
    ('physical_medicine_rehabilitation', 'Physical Medicine & Rehabilitation (PM&R)', 38),
    ('plastic_surgery', 'Plastic Surgery', 39),
    ('pulmonology', 'Pulmonology', 40),
    ('radiation_oncology', 'Radiation Oncology', 41),
    ('rheumatology', 'Rheumatology', 42),
    ('sleep_medicine', 'Sleep Medicine', 43),
    ('sports_medicine', 'Sports Medicine', 44),
    ('surgical_oncology', 'Surgical Oncology', 45),
    ('thoracic_surgery', 'Thoracic Surgery', 46),
    ('trauma_surgery', 'Trauma Surgery', 47),
    ('urology', 'Urology', 48),
    ('vascular_surgery', 'Vascular Surgery', 49),
    ('other', 'Other', 50);