import { Injectable, NotFoundException, HttpStatus } from '@nestjs/common';
import { UtilsService } from 'src/common/utils/utils.service';
import { PrescriptionsRepository } from './prescriptions.repository';
import { TranscriptionsRepository } from 'src/transcriptions/transcriptions.repository';
import {
  LLM_RESPONSETYPES,
  responseTypes,
  TRANSCRIPTION_TYPES,
} from 'src/common/constants/common.constants';
import {
  CreatePrescriptionDto,
  PrescriptionDetailsDto,
  UpdatePrescriptionDto,
} from './dtos/prescriptions.dto';

@Injectable()
export class PrescriptionsService {
  constructor(
    private readonly utilsService: UtilsService,
    private readonly prescriptionRepository: PrescriptionsRepository,
    private readonly transcriptionsRepository: TranscriptionsRepository,
  ) {}
  async generatePrescriptionsFromTranscriptions(
    encounter_id: string,
    userEmail: string,
  ) {
    try {
      const existingPrescriptions =
        await this.prescriptionRepository.getPrescriptionsByEncounterId(
          encounter_id,
        );
      // if labs already exist for this encounter, return them
      if (existingPrescriptions.length > 0) {
        return {
          Prescriptions: existingPrescriptions.filter((prescription) => {
            // Check if all relevant fields are "Not provided"
            const allFieldsNotProvided =
              prescription.drug_name === '-' &&
              prescription.dose === '-' &&
              prescription.sig === '-' &&
              prescription.brand === '-' &&
              prescription.patient_notes === '-';

            // Keep this item only if NOT all fields are "Not provided"
            return !allFieldsNotProvided;
          }),
        };
      }

      // get transcription for encounter
      const transcription =
        await this.transcriptionsRepository.getTranscriptionUsingEncounterId(
          encounter_id,
        );

      // if no transcription exists for this encounter, throw error
      if (!transcription) {
        throw new NotFoundException(
          'No transcription found for this encounter',
        );
      }

      const combinedTranscription = transcription
        .filter((tr) => tr.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL)
        .map((tr) => tr.transcription)
        .filter((t) => t)
        .join(' ');

      // generate prescriptions from transcription
      const prescriptionsdata = await this.utilsService.genereteResponseFromLLM(
        'gpt-4o',
        'user',
        `Based on the provided transcript, generate a medical prescription with the following columns: Drug Name, Dose, SIG, Brand, and Patient Notes. For any cell where information is unavailable in the transcript, include "-" as the value for that field. Ensure that Indian brands are mentioned where applicable. Provide the response in pure JavaScript JSON format that can be directly parsed, without including Markdown, special characters, or any formatting outside the JSON structure. Example Output Structure:
          {
            "prescription": [
              {
                "Drug Name": "Drug Name Here",
                "Dose": "Dose Here",
                "SIG": "SIG Instructions Here",
                "Brand": "Brand Name Here",
                "Patient Notes": "Patient Notes Here"
              },
              {
                "Drug Name": "Drug Name Here",
                "Dose": "Dose Here",
                "SIG": "SIG Instructions Here",
                "Brand": "Brand Name Here",
                "Patient Notes": "Patient Notes Here"
              }
            ]
          }
          Transcript: ${combinedTranscription}`,
        1500,
        0.6,
        LLM_RESPONSETYPES.JSON,
        responseTypes.Prescriptions,
        transcription
          .filter(
            (tr) => tr.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL,
          )
          .map((tr) => tr.id),
        userEmail,
      );

      if (!prescriptionsdata) {
        throw new Error('Error generating prescriptions from transcription');
      }

      let parsedJson = JSON.parse(prescriptionsdata);
      if (
        !parsedJson?.prescription &&
        !parsedJson?.Prescription &&
        !parsedJson?.prescriptions &&
        !parsedJson?.Prescriptions
      ) {
        if (
          !parsedJson?.hasOwnProperty('Drug Name') ||
          !parsedJson?.hasOwnProperty('Dose') ||
          !parsedJson?.hasOwnProperty('SIG') ||
          !parsedJson?.hasOwnProperty('Brand') ||
          !parsedJson?.hasOwnProperty('Patient Notes')
        ) {
          throw this.utilsService.formatErrorResponse(
            new Error('JSON format not correct'),
            'Error generating prescriptions from transcription json format not correct',
            HttpStatus.BAD_REQUEST
          );
        }
      }
      let prescription_data = parsedJson?.prescription ||
        parsedJson?.Prescription ||
        parsedJson?.prescriptions ||
        parsedJson?.Prescriptions || [parsedJson];

      let data: { Prescriptions: PrescriptionDetailsDto[] } = {
        Prescriptions:
          prescription_data.length > 0
            ? prescription_data
                .filter((prescription) => {
                  // Check if all relevant fields are "Not provided"
                  const allFieldsNotProvided =
                    prescription['Drug Name'] === '-' &&
                    prescription.Dose === '-' &&
                    prescription.SIG === '-' &&
                    prescription.Brand === '-' &&
                    prescription['Patient Notes'] === '-';

                  // Keep this item only if NOT all fields are "Not provided"
                  return !allFieldsNotProvided;
                })
                .map((prescription) => ({
                  drug_name: prescription['Drug Name'],
                  dose: prescription.Dose,
                  sig: prescription.SIG,
                  brand: prescription.Brand,
                  patient_notes: prescription['Patient Notes'],
                  encounter_id: encounter_id,
                  status: 'PENDING',
                  created_by: 'Transcript',
                }))
            : [],
      };

      let createPrescription: CreatePrescriptionDto = {
        info: [...data.Prescriptions],
      };
      // create Prescriptions in database
      const createdPrescriptions =
        await this.prescriptionRepository.createPrescriptions(
          createPrescription,
          userEmail,
        );
      if (!createdPrescriptions) {
        throw new Error('Error creating prescriptions in db');
      }

      return { Prescriptions: createdPrescriptions };
    } catch (error) {
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to generate prescriptions from transcription',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async createPrescription(model: CreatePrescriptionDto, userEmail: string) {
    return await this.prescriptionRepository.createPrescriptions(
      model,
      userEmail,
    );
  }

  async updatePrescription(
    model: UpdatePrescriptionDto,
    prescription_id: string,
    userEmail: string,
  ) {
    return await this.prescriptionRepository.updatePrescription(
      model,
      prescription_id,
      userEmail,
    );
  }

  async deletePrescription(prescription_id: string, userEmail: string) {
    return await this.prescriptionRepository.deletePrescription(
      prescription_id,
      userEmail,
    );
  }
}
