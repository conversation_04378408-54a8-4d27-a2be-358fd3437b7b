import { Modu<PERSON> } from '@nestjs/common';
import { TranscribeController } from './transcribe.controller';
import { TranscribeService } from './transcribe.service';
import { ConfigService } from '@nestjs/config';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';
import { TranscribeRepository } from './transcribe.repository';
import { LiveTranscribeGateway } from './live-transcribe.gateway';
import { LiveTranscribeService } from './live-transcribe.service';
@Module({
  controllers: [TranscribeController],
  providers: [
    TranscribeService,
    ConfigService,
    s3FileUploadService,
    TranscribeRepository,
    LiveTranscribeGateway,
    LiveTranscribeService
  ],
  exports: [TranscribeService],
})
export class TranscribeModule {}
