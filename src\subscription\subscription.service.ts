import { ForbiddenException, Injectable } from '@nestjs/common';
import { SubscriptionRepository } from './subscription.repository';
import { CreateSubscriptionDto } from './dto/subscription.dto';
import { PrismaService } from 'src/common/prisma/prisma.service';

@Injectable()
export class SubscriptionService {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly prismaservice: PrismaService,
  ) {}

  async createSubsciption(userEmail: string, model: CreateSubscriptionDto) {
    //check the purchaser belongs to admin  or not
    const isAdminCount = await this.prismaservice.admin.count({
      where: {
        email: userEmail,
        isActive:true
      },
    });

    if (isAdminCount == 0) {
      throw new ForbiddenException('you are not an admin to subscribe any plan');
    }

    return await this.subscriptionRepository.createSubscription(
      userEmail,
      model,
    );
  }
}
