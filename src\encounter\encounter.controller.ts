import {
  Body,
  Controller,
  Delete,
  Get,
  Optional,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  CreateEncounterDTO,
  CreateQuickEncounterDTO,
  EncounterDetailsDTO,
} from './dtos';
import { EncounterService } from './encounter.service';
import {
  ApiResponseDTO,
  PaginatedResultDTO,
  PaginationQueryDTO,
} from '../common/dtos';
import { GetUserEmail } from '../common/decorators/get-user-email.decorator';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Encounter } from '@prisma/client';
import { EncounterSummeryDto } from './dtos/EncounterDetails.dto';
import { PERMISSIONS } from 'src/common/decorators/permissions.decorator';

@ApiTags('Encounters')
@ApiBearerAuth('access-token')
@Controller('encounters')
export class EncounterController {
  constructor(private readonly encounterService: EncounterService) {}

  @Post()
  @UsePipes(new ValidationPipe())
  @ApiOperation({ summary: 'Create a new medical encounter' })
  @PERMISSIONS('visit.create.self', 'visit.create.org')
  async createEncounter(
    @Body() data: CreateEncounterDTO,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<EncounterDetailsDTO>> {
    const encounter = await this.encounterService.insertEncounter(
      data,
      userEmail,
    );
    return {
      data: encounter,
    };
  }

  @Get(':encounterId')
  @ApiOperation({ summary: 'Get encounter for encounterId' })
  @PERMISSIONS('visit.read.self', 'visit.read.org')
  async getEncounterById(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
  ): Promise<ApiResponseDTO<EncounterDetailsDTO>> {
    const encounter = await this.encounterService.getEncounterById(encounterId);
    return {
      statusCode: 200,
      message: 'Encounter Details fetched successfully',
      data: encounter,
    };
  }

  @Get()
  @ApiOperation({
    summary: 'Retrieve all encounters for the authenticated doctor',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'patient_name',
    required: false,
    type: String,
    description: 'Filter encounters by patient name',
  })
  @ApiQuery({
    name: 'patient_id',
    required: false,
    type: String,
    description: 'Filter encounters by patient id',
  })
  @PERMISSIONS('visit.read.self', 'visit.read.org')
  async getAllEncounters(
    @Query() pagination: PaginationQueryDTO,
    @GetUserEmail() userEmail: string,
    @Query('patient_name') patient_name: string,
    @Query('patient_id') patient_id: string,
    @Req() req: Request,
  ): Promise<PaginatedResultDTO<EncounterDetailsDTO>> {
    const { page, limit } = pagination;

    if (!patient_name || patient_name == '') {
      patient_name = undefined;
    }
    if (!patient_id || patient_id == '') {
      patient_id = undefined;
    }

    return await this.encounterService.getAllEncountersByDoctorEmail(
      userEmail,
      page,
      limit,
      patient_name,
      patient_id,
    );
  }

  @Patch(':encounterId/update-status/:status')
  @PERMISSIONS('visit.update.self', 'visit.update.org','recording.create.self','recording.create.org')
  @ApiOperation({ summary: 'Update encounter status' })
  async updateEncounterStatus(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @Param('status') status: string,
  ): Promise<ApiResponseDTO<Encounter>> {
    const encounter = await this.encounterService.updateEncounterStatus(
      encounterId,
      status,
    );
    return {
      data: encounter,
    };
  }

  @Patch('/sign-off/:encounterId')
  @PERMISSIONS('visit.signoff.self', 'visit.signoff.org')
  async singOffEncounter(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.encounterService.signOffEncounter(encounterId, userEmail);
  }
  @Delete('/:encounterId')
  @PERMISSIONS('visit.delete.self', 'visit.delete.org')
  async deleteEncounter(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.encounterService.deleteEncounter(encounterId, userEmail);
  }

  @Post('/summary/:encounterId')
  @PERMISSIONS('visit.read.self', 'visit.read.org')
  async getEncounterSummary(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @Body() model: EncounterSummeryDto,
  ) {
    return await this.encounterService.getEncounterSummary(encounterId, model);
  }

  @Post('/quick-encounter')
  @PERMISSIONS('visit.create.self', 'visit.create.org')
  async createQuickEncounter(
    @Body() data: CreateQuickEncounterDTO,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<EncounterDetailsDTO>> {
    const encounter = await this.encounterService.insertEncounter(
      data,
      userEmail,
    );
    return {
      data: encounter,
    };
  }

  @Patch('/:encounterId/patient/:patientId')
  @PERMISSIONS('visit.update.self', 'visit.update.org')
  async updateEncounterPatient(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.encounterService.updateEncounterPatient(
      { encounter_id: encounterId, patient_id: patientId },
      userEmail,
    );
  }
}
