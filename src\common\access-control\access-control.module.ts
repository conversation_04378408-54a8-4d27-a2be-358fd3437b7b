import { Module } from '@nestjs/common';
import { AccessControlService } from './access-control.service';
import { AccessControlController } from './access-control.controller';
import { AccessControlRepository } from './access-control.repository';

@Module({
  controllers: [AccessControlController],
  providers: [AccessControlService, AccessControlRepository],
  exports: [AccessControlService, AccessControlRepository],
})
export class AccessControlModule {}
