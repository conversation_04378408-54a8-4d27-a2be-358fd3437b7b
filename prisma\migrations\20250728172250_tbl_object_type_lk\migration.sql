-- CreateTable
CREATE TABLE "tbl_object_type_lk" (
    "object_type_id" UUID NOT NULL,
    "object_type_name" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_object_type_lk_pkey" PRIMARY KEY ("object_type_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tbl_object_type_lk_object_type_name_key" ON "tbl_object_type_lk"("object_type_name");
