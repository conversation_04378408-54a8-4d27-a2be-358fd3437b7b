import { ApiProperty } from '@nestjs/swagger';
import { PatientDetailsDTO } from '../../patient/dtos/PatientDetails.dto';
import { DoctorDetailsDTO } from '../../doctor/dtos/DoctorDetails.dto';
import { IsBoolean } from 'class-validator';

export class EncounterDetailsDTO {
  @ApiProperty({
    description: 'Unique identifier for the encounter',
    example: 'encounter-uuid-789',
  })
  encounterId: string;

  @ApiProperty({
    description: 'Date and time of the encounter',
    example: '2024-01-01T10:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  encounterDate: Date;

  @ApiProperty({
    description: 'Details of the patient involved in the encounter',
    type: PatientDetailsDTO,
  })
  patient: PatientDetailsDTO;

  @ApiProperty({
    description: 'Details of the doctor involved in the encounter',
    type: DoctorDetailsDTO,
  })
  doctor: DoctorDetailsDTO;
  encounterStatus: string;
  noteTypeId?: string;
}

export class EncounterSummeryDto {
  @ApiProperty({ description: 'notes for the encounter' })
  @IsBoolean()
  Notes: boolean;

  @ApiProperty({ description: 'labs of an encounte' })
  @IsBoolean()
  Labs: boolean;

  @ApiProperty({ description: 'prescription for the encounter' })
  @IsBoolean()
  Prescriptions: boolean;
}
