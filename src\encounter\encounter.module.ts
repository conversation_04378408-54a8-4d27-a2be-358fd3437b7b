import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { EncounterController } from './encounter.controller';
import { EncounterService } from './encounter.service';
import { EncounterRepository } from './encounter.repository';
import { DoctorRepository } from '../doctor/doctor.repository';
import { PatientModule } from 'src/patient/patient.module';
import { PatientRepository } from 'src/patient/patient.repository';
import { ConfigService } from '@nestjs/config';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';

@Module({
  imports: [PatientModule],
  controllers: [EncounterController],
  providers: [
    EncounterService,
    EncounterRepository,
    DoctorRepository,
    PatientRepository,
    ConfigService,
    s3FileUploadService,
  ],
  exports: [EncounterRepository,EncounterService],
})
export class EncounterModule {}
