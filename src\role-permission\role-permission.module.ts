import { Module } from '@nestjs/common';
import { RolePermissionController } from './role-permission.controller';
import { RolePermissionService } from './role-permission.service';
import { RolePermissionRepository } from './role-permission.repository';

@Module({
  controllers: [RolePermissionController],
  providers: [RolePermissionService,RolePermissionRepository]
})
export class RolePermissionModule {}
