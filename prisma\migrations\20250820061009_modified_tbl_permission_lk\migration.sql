/*
  Warnings:

  - You are about to drop the column `create` on the `tbl_permission_lk` table. All the data in the column will be lost.
  - You are about to drop the column `delete` on the `tbl_permission_lk` table. All the data in the column will be lost.
  - You are about to drop the column `object_type_value_id` on the `tbl_permission_lk` table. All the data in the column will be lost.
  - You are about to drop the column `read` on the `tbl_permission_lk` table. All the data in the column will be lost.
  - You are about to drop the column `update` on the `tbl_permission_lk` table. All the data in the column will be lost.
  - You are about to drop the column `view_enable` on the `tbl_permission_lk` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[key]` on the table `tbl_permission_lk` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `key` to the `tbl_permission_lk` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "tbl_permission_lk_object_type_value_id_idx";

-- AlterTable
ALTER TABLE "tbl_permission_lk" DROP COLUMN "create",
DROP COLUMN "delete",
DROP COLUMN "object_type_value_id",
DROP COLUMN "read",
DROP COLUMN "update",
DROP COLUMN "view_enable",
ADD COLUMN     "description" TEXT,
ADD COLUMN     "key" TEXT NOT NULL,
ADD COLUMN     "type" TEXT DEFAULT 'SYSTEM_DEFINED';

-- CreateIndex
CREATE UNIQUE INDEX "tbl_permission_lk_key_key" ON "tbl_permission_lk"("key");
