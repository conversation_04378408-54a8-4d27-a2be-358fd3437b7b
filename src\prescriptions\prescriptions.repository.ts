import { HttpStatus, Injectable, NotFoundException, Logger } from '@nestjs/common';
import { Prescriptions } from '@prisma/client';
import { PrismaService } from 'src/common/prisma/prisma.service';
import {
  CreatePrescriptionDto,
  UpdatePrescriptionDto,
} from './dtos/prescriptions.dto';
import { UtilsService } from 'src/common/utils/utils.service';
@Injectable()
export class PrescriptionsRepository {
  private readonly logger = new Logger(PrescriptionsRepository.name);
  constructor(private readonly prismaservice: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}
  async getPrescriptionsByEncounterId(
    encounterId: string,
  ): Promise<Prescriptions[]> {
    try {
      const prescriptions = await this.prismaservice.prescriptions.findMany({
        where: {
          encounter_id: encounterId,
          isActive: true,
        },
      });
      return prescriptions;
    } catch (error) {
      this.logger.error('Error fetching prescriptions:', error?.stack);
        throw this.utilsService.formatErrorResponse(
          error,
          'Failed to fetch prescriptions',
          HttpStatus.INTERNAL_SERVER_ERROR
        );
    }
  }

  async createPrescriptions(
    data: CreatePrescriptionDto,
    userEmail: string,
  ): Promise<Prescriptions[]> {
    try {
      const prescriptions =
        await this.prismaservice.prescriptions.createManyAndReturn({
          data: data.info.map((info) => ({
            drug_name: info.drug_name,
            encounter_id: info.encounter_id,
            dose: info.dose || null,
            sig: info.sig || null,
            brand: info.brand || null,
            patient_notes: info.patient_notes || null,
            created_by:
              info.created_by && info.created_by != ''
                ? info.created_by
                : userEmail,
            status: info.status || null,
          })),
        });
      return prescriptions;
    } catch (error) {
      this.logger.error('Error creating prescriptions:', error?.stack);
        throw this.utilsService.formatErrorResponse(
          error,
          'Failed to create prescriptions',
          HttpStatus.INTERNAL_SERVER_ERROR
        );
    }
  }

  async updatePrescription(
    model: UpdatePrescriptionDto,
    prescription_id: string,
    userEmail: string,
  ): Promise<Prescriptions> {
    try {
      const existingLab = await this.prismaservice.prescriptions.findFirst({
        where: {
          prescription_id: prescription_id,
          isActive: true,
        },
        select: {
          prescription_id: true,
        },
      });

      if (!existingLab) {
        this.logger.error(`Prescription not found for the given prescription ID: ${prescription_id}`);
        throw new NotFoundException('Prescription not found');
      }

      const prescription = await this.prismaservice.prescriptions.update({
        where: {
          prescription_id: prescription_id,
          isActive: true,
        },
        data: {
          drug_name: model.drug_name,
          dose: model.dose || null,
          sig: model.sig || null,
          brand: model.brand || null,
          patient_notes: model.patient_notes || null,
          updated_at: new Date(),
          updated_by: userEmail,
        },
      });
      return prescription;
    } catch (error) {
      this.logger.error('Error updating prescription:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update prescription',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async deletePrescription(
    prescription_id: string,
    userEmail: string,
  ): Promise<Prescriptions> {
    try {
      const existingPrescription =
        await this.prismaservice.prescriptions.findFirst({
          where: {
            prescription_id: prescription_id,
            isActive: true,
          },
          select: {
            prescription_id: true,
          },
        });

      if (!existingPrescription) {
        this.logger.error(`Prescription not found for the given prescription ID: ${prescription_id}`);
        throw new NotFoundException('Prescription not found');
      }

      const prescription = await this.prismaservice.prescriptions.update({
        where: {
          prescription_id: prescription_id,
          isActive: true,
        },
        data: {
          status: 'REJECTED',
          updated_at: new Date(),
          updated_by: userEmail,
        },
      });
      return prescription;
    } catch (error) {
      this.logger.error('Error deleting Prescription:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete prescription',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
