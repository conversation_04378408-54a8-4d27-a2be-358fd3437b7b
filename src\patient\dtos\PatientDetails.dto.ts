import { ApiProperty } from '@nestjs/swagger';
import { Gender } from '@prisma/client';

export class PatientDetailsDTO {
  @ApiProperty({
    description: 'Unique identifier for the patient',
    example: 'patient-uuid-123',
  })
  patientId: string;

  @ApiProperty({
    description: 'First name of the patient',
    example: 'Alice',
  })
  patientFirstName: string;

  @ApiProperty({
    description: 'Last name of the patient',
    example: 'Johnson',
  })
  patientLastName: string;

  @ApiProperty({
    description: 'Gender of the patient',
    enum: Gender,
    example: Gender.FEMALE,
  })
  patientGender: Gender;

  @ApiProperty({
    description: 'Date of birth of the patient',
    example: '1990-01-01',
    type: String,
    format: 'date',
  })
  patientDOB: string;

  @ApiProperty({
    description: 'Phone number of the patient',
    example: '+************',
  })
  patientPhone: string;

  @ApiProperty({
    description: "Country code of the patient's phone number",
    example: '+91',
    maxLength: 5,
  })
  phoneCountryCode: string;
}
