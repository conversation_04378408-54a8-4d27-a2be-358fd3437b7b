import { Injectable, Logger, NotFoundException, HttpStatus } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';
import { tbl_user_permission_mapping } from '@prisma/client';
import { CreateUserPermissionMappingDto } from './dtos/create-user-permission-mapping.dto';

@Injectable()
export class UserPermissionRepository {
  private readonly logger = new Logger(UserPermissionRepository.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async create(
    dto: CreateUserPermissionMappingDto,
    userEmail: string,
  ): Promise<tbl_user_permission_mapping> {
    try {
      return await this.prisma.tbl_user_permission_mapping.create({
        data: {
          user_id: dto.user_id,
          permission_id: dto.permission_id,
          type: dto.type ?? 'SYSTEM_DEFINED',
          created_by: userEmail,
        },
      });
    } catch (error) {
      this.logger.error('Error creating mapping:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: string): Promise<tbl_user_permission_mapping> {
    try {
      const mapping = await this.prisma.tbl_user_permission_mapping.findUnique({
        where: { user_permission_id: id },
        include: {
          tbl_user_details: true,
          tbl_permission_lk: true,
        },
      });

      if (!mapping) {
        throw new NotFoundException('Mapping not found');
      }

      return mapping;
    } catch (error) {
      this.logger.error('Error fetching mapping:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch mapping',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async findAll(): Promise<tbl_user_permission_mapping[]> {
    return await this.prisma.tbl_user_permission_mapping.findMany({
      where: { isActive: true },
      include: {
        tbl_user_details: true,
        tbl_permission_lk: true,
      },
    });
  }

  async update(
    id: string,
    dto: Partial<CreateUserPermissionMappingDto>,
    userEmail: string,
  ): Promise<tbl_user_permission_mapping> {
    try {
      return await this.prisma.tbl_user_permission_mapping.update({
        where: { user_permission_id: id },
        data: {
          ...dto,
          updated_by: userEmail,
        },
        include: {
          tbl_user_details: true,
          tbl_permission_lk: true,
        },
      });
    } catch (error) {
      this.logger.error('Error updating mapping:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async remove(id: string, userEmail: string): Promise<tbl_user_permission_mapping> {
    try {
      return await this.prisma.tbl_user_permission_mapping.update({
        where: { user_permission_id: id },
        data: {
          isActive: false,
          updated_by: userEmail,
        },
      });
    } catch (error) {
      this.logger.error('Error deleting mapping:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
