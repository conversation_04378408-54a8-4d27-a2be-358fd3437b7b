import { ApiPropertyOptional } from '@nestjs/swagger';

export class PaginationResDTO {
  @ApiPropertyOptional({
    description: 'Current page number',
    example: 1,
  })
  currentPage?: number;

  @ApiPropertyOptional({
    description: 'Number of records per page',
    example: 10,
  })
  recordsPerPage?: number;

  @ApiPropertyOptional({
    description: 'Total number of records',
    example: 100,
  })
  totalRecords?: number;

  @ApiPropertyOptional({
    description: 'Total number of pages',
    example: 10,
  })
  totalPages?: number;
}
