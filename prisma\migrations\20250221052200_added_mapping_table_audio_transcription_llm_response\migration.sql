-- CreateTable
CREATE TABLE "audio_transcription_llm_response" (
    "id" UUID NOT NULL,
    "transcription_id" UUID,
    "response_id" UUID,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "audio_transcription_llm_response_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "audio_transcription_llm_response" ADD CONSTRAINT "audio_transcription_llm_response_transcription_id_fkey" FOREIGN KEY ("transcription_id") REFERENCES "audio_transcription"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "audio_transcription_llm_response" ADD CONSTRAINT "audio_transcription_llm_response_response_id_fkey" FOREI<PERSON>N KEY ("response_id") REFERENCES "response_llm"("responseId") ON DELETE SET NULL ON UPDATE CASCADE;
