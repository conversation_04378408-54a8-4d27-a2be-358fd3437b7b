generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Doctor {
  doctor_id           String             @id @default(uuid()) @db.Uuid
  email               String             @unique
  first_name          String?
  last_name           String?
  created_at          DateTime           @default(now())
  created_by          String             @default("ADMIN")
  updated_at          DateTime?          @updatedAt
  updated_by          String?
  isActive            Boolean            @default(true)
  phone               String?
  isVerified          Boolean?           @default(false)
  country_of_practice String?
  ehr                 String?
  specialty           String?
  specialties         Specialties?       @relation(fields: [specialty], references: [name])
  DoctorLetterHead    DoctorLetterHead[]
  encounters          Encounter[]
  NoteHeader          NoteHeader[]
  Subscription        Subscription[]
  TenantDoctor        TenantDoctor[]

  @@map("doctor")
}

model Patient {
  patient_id         String    @id @default(uuid()) @db.Uuid
  first_name         String?
  last_name          String?
  date_of_birth      DateTime? @db.Timestamptz(3)
  gender             Gender
  created_at         DateTime  @default(now())
  created_by         String    @default("ADMIN")
  updated_at         DateTime? @updatedAt
  updated_by         String?
  phone_country_code String?   @db.VarChar(5)
  phone_number       String?   @db.VarChar(20)
  isActive           Boolean   @default(true)
  tenant_id          String?   @db.Uuid
  external_id_1      String?
  external_id_2      String?

  // 🔹 Newly Added Fields
  patient_image String?
  address       String?
  district_name String?
  state         String?
  pin           String?
  country       String?

  encounters           Encounter[]
  NoteHeader           NoteHeader[]
  tenant               Tenant?              @relation(fields: [tenant_id], references: [Id])
  audio_transcriptions AudioTranscription[] @relation("AudioTranscriptionToPatient")

  @@index([phone_number], map: "idx_patient_phone_number")
  @@index([created_by], map: "idx_patient_created_by")
  @@map("patient")
}

model Encounter {
  encounter_id           String                   @id @default(uuid()) @db.Uuid
  patient_id             String                   @db.Uuid
  encounter_date         DateTime                 @db.Date
  created_at             DateTime                 @default(now())
  created_by             String                   @default("ADMIN")
  updated_at             DateTime?                @updatedAt
  updated_by             String?
  doctor_id              String                   @db.Uuid
  isActive               Boolean                  @default(true)
  note_type_id           String?                  @db.Uuid
  encounter_status       String?                  @db.VarChar(50)
  note_header            String?
  audio_transcriptions   AudioTranscription[]
  doctor                 Doctor                   @relation(fields: [doctor_id], references: [doctor_id])
  encounterstatus        EncounterStatus?         @relation(fields: [encounter_status], references: [status])
  note_type              NoteType?                @relation(fields: [note_type_id], references: [note_type_id])
  patient                Patient                  @relation(fields: [patient_id], references: [patient_id])
  EncounterFeedback      EncounterFeedback[]
  labs                   Labs[]
  NoteHeader             NoteHeader[]
  Prescriptions          Prescriptions[]
  tbl_vital_measurements tbl_vital_measurements[]

  @@map("encounter")
}

model AudioTranscription {
  id                             String                           @id @default(uuid()) @db.Uuid
  original_filename              String                           @db.VarChar(255)
  s3_uri                         String                           @db.VarChar(512)
  file_size                      BigInt
  mime_type                      String                           @db.VarChar(100)
  transcription                  String?
  detected_language              String?                          @db.VarChar(10)
  patient_id                     String?                          @db.Uuid
  encounter_id                   String?                          @db.Uuid
  created_at                     DateTime                         @default(now())
  created_by                     String                           @default("SYSTEM") @db.VarChar(100)
  updated_at                     DateTime                         @updatedAt
  updated_by                     String                           @default("SYSTEM") @db.VarChar(100)
  is_processed                   Boolean                          @default(false)
  processing_error               String?
  isActive                       Boolean                          @default(true)
  raw_transcription              String?
  duration                       String?
  transcription_type             String?
  encounter                      Encounter?                       @relation(fields: [encounter_id], references: [encounter_id])
  transcriptionType              TranscriptionType?               @relation(fields: [transcription_type], references: [transcription_type])
  AudioTranscription_LLMResponse AudioTranscription_LLMResponse[]
  ResponseLLM                    ResponseLLM[]
  Patient                        Patient[]                        @relation("AudioTranscriptionToPatient")

  @@index([patient_id], map: "idx_audio_transcription_patient")
  @@index([encounter_id], map: "idx_audio_transcription_encounter")
  @@map("audio_transcription")
}

model NoteHeader {
  note_id      String       @id @default(uuid()) @db.Uuid
  patient_id   String       @db.Uuid
  encounter_id String       @db.Uuid
  doctor_id    String       @db.Uuid
  note_type_id String       @db.Uuid
  created_at   DateTime     @default(now())
  created_by   String       @default("ADMIN")
  updated_at   DateTime?    @updatedAt
  updated_by   String?
  isActive     Boolean      @default(true)
  note_details NoteDetail[]
  doctor       Doctor       @relation(fields: [doctor_id], references: [doctor_id])
  encounter    Encounter    @relation(fields: [encounter_id], references: [encounter_id])
  note_type    NoteType     @relation(fields: [note_type_id], references: [note_type_id])
  patient      Patient      @relation(fields: [patient_id], references: [patient_id])

  @@map("note_header")
}

model NoteDetail {
  note_detail_id  String      @id @default(uuid()) @db.Uuid
  note_header_id  String      @db.Uuid
  note_section_id String      @db.Uuid
  value           String?
  created_at      DateTime    @default(now())
  created_by      String      @default("ADMIN")
  updated_at      DateTime?   @updatedAt
  updated_by      String?
  isActive        Boolean     @default(true)
  note_header     NoteHeader  @relation(fields: [note_header_id], references: [note_id])
  note_section    NoteSection @relation(fields: [note_section_id], references: [note_section_id])

  @@map("note_detail")
}

model NoteType {
  note_type_id       String            @id @default(uuid()) @db.Uuid
  note_type_name     String            @unique
  created_at         DateTime          @default(now())
  created_by         String            @default("ADMIN")
  isActive           Boolean           @default(true)
  updated_at         DateTime?         @updatedAt
  updated_by         String?
  Encounter          Encounter[]
  note_headers       NoteHeader[]
  note_type_sections NoteTypeSection[]

  @@map("note_type_lk")
}

model NoteSection {
  note_section_id    String            @id @default(uuid()) @db.Uuid
  section_name       String            @unique
  created_at         DateTime          @default(now())
  created_by         String            @default("ADMIN")
  isActive           Boolean           @default(true)
  updated_at         DateTime?         @updatedAt
  updated_by         String?
  note_details       NoteDetail[]
  note_type_sections NoteTypeSection[]

  @@map("note_section_lk")
}

model NoteTypeSection {
  note_type_section_id String      @id @default(uuid()) @db.Uuid
  note_type_id         String      @db.Uuid
  note_section_id      String      @db.Uuid
  created_at           DateTime    @default(now())
  created_by           String      @default("ADMIN")
  isActive             Boolean     @default(true)
  updated_at           DateTime?   @updatedAt
  updated_by           String?
  order                Int?
  note_section         NoteSection @relation(fields: [note_section_id], references: [note_section_id])
  note_type            NoteType    @relation(fields: [note_type_id], references: [note_type_id])

  @@map("note_type_sections")
}

model EncounterStatus {
  status      String      @id
  label       String      @db.VarChar(50)
  description String?
  isActive    Boolean     @default(true)
  created_at  DateTime    @default(now())
  created_by  String      @default("ADMIN")
  updated_at  DateTime?   @updatedAt
  updated_by  String?
  Encounter   Encounter[]

  @@map("encounter_status")
}

model Labs {
  lab_id        String     @id @default(uuid()) @db.Uuid
  lab_name      String
  isActive      Boolean    @default(true)
  notes         String?
  reason        String?
  encounter_id  String?    @db.Uuid
  created_at    DateTime   @default(now())
  created_by    String     @default("ADMIN")
  updated_at    DateTime?  @updatedAt
  updated_by    String?
  status        String?
  status_reason String?
  encounter     Encounter? @relation(fields: [encounter_id], references: [encounter_id])
  Status        Status?    @relation(fields: [status], references: [status])

  @@index([encounter_id], map: "idx_labs_encounter")
  @@map("labs")
}

model Status {
  status        String          @id
  label         String          @db.VarChar(50)
  description   String?
  isActive      Boolean         @default(true)
  created_at    DateTime        @default(now())
  created_by    String          @default("ADMIN")
  updated_at    DateTime?       @updatedAt
  updated_by    String?
  Labs          Labs[]
  Prescriptions Prescriptions[]

  @@map("status")
}

model ResponseType {
  type_name   String        @id
  label       String        @db.VarChar(50)
  description String?
  isActive    Boolean       @default(true)
  created_at  DateTime      @default(now())
  created_by  String        @default("ADMIN")
  updated_at  DateTime?     @updatedAt
  updated_by  String?
  ResponseLLM ResponseLLM[]

  @@map("response_type")
}

model ResponseLLM {
  responseId                     String                           @id @default(uuid()) @db.Uuid
  raw_response                   String
  successful                     Boolean                          @default(false)
  transcriptionId                String?                          @db.Uuid
  response_type                  String?
  created_at                     DateTime                         @default(now())
  created_by                     String                           @default("ADMIN")
  updated_at                     DateTime?                        @updatedAt
  updated_by                     String?
  isActive                       Boolean                          @default(true)
  AudioTranscription_LLMResponse AudioTranscription_LLMResponse[]
  responseType                   ResponseType?                    @relation(fields: [response_type], references: [type_name])
  audioTranscription             AudioTranscription?              @relation(fields: [transcriptionId], references: [id])

  @@map("response_llm")
}

model Prescriptions {
  prescription_id String     @id @default(uuid()) @db.Uuid
  drug_name       String?
  dose            String?
  sig             String?
  brand           String?
  isActive        Boolean    @default(true)
  encounter_id    String?    @db.Uuid
  status          String?
  status_reason   String?
  created_at      DateTime   @default(now())
  created_by      String     @default("ADMIN")
  updated_at      DateTime?  @updatedAt
  updated_by      String?
  patient_notes   String?
  encounter       Encounter? @relation(fields: [encounter_id], references: [encounter_id])
  Status          Status?    @relation(fields: [status], references: [status])

  @@index([encounter_id], map: "idx_prescriptions_encounter")
  @@map("prescriptions")
}

model DoseUnit {
  dose_unit_id String    @id @default(uuid()) @db.Uuid
  name         String
  full_name    String?
  group        String?
  created_at   DateTime  @default(now())
  created_by   String    @default("ADMIN")
  updated_at   DateTime? @updatedAt
  updated_by   String?

  @@map("dose_unit_lk")
}

model Plan {
  planId            String         @id @default(uuid()) @db.Uuid
  name              String
  description       String?
  isActive          Boolean        @default(true)
  effectiveDateFrom DateTime?      @default(now())
  effectiveDateTo   DateTime?
  created_at        DateTime?      @default(now())
  created_by        String         @default("ADMIN")
  updated_at        DateTime?      @updatedAt
  updated_by        String?
  Subscription      Subscription[]

  @@map("plan")
}

model Subscription {
  subscriptionId    String    @id @default(uuid()) @db.Uuid
  planId            String?   @db.Uuid
  doctor_id         String?   @db.Uuid
  isActive          Boolean   @default(true)
  status            String?   @default("ACTIVE")
  created_at        DateTime  @default(now())
  created_by        String    @default("ADMIN")
  updated_at        DateTime? @updatedAt
  updated_by        String?
  effectiveDateFrom DateTime  @default(now())
  effectiveDateTo   DateTime?
  doctor            Doctor?   @relation(fields: [doctor_id], references: [doctor_id])
  plan              Plan?     @relation(fields: [planId], references: [planId])

  @@map("subscription")
}

model Admin {
  adminId    String    @id @default(uuid()) @db.Uuid
  email      String?
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?
  isActive   Boolean   @default(true)

  @@map("admin")
}

model Specialties {
  name        String    @id
  label       String?
  description String?
  order       Int?
  isActive    Boolean   @default(true)
  created_at  DateTime  @default(now())
  created_by  String    @default("ADMIN")
  updated_at  DateTime? @updatedAt
  updated_by  String?
  Doctor      Doctor[]

  @@map("specialties")
}

model DoctorLetterHead {
  letterhead_id             String                      @id @default(uuid()) @db.Uuid
  page_size                 String?
  page_width                Float?
  top_padding               Float?
  top_padding_px            Int?
  bottom_padding            Float?
  bottom_padding_px         Int?
  units                     String?
  type                      String?
  doctor_id                 String?                     @db.Uuid
  is_default                Boolean?
  isActive                  Boolean                     @default(true)
  created_at                DateTime                    @default(now())
  created_by                String                      @default("ADMIN")
  updated_at                DateTime?                   @updatedAt
  updated_by                String?
  page_height               Float?
  doctor                    Doctor?                     @relation(fields: [doctor_id], references: [doctor_id])
  doctor_letterhead_section doctor_letterhead_section[]

  @@index([doctor_id], map: "idx_doctor_letterhead_doctor")
  @@map("doctor_letterhead")
}

model PageSize {
  page_size_id String    @id @default(uuid()) @db.Uuid
  page_size    String
  page_height  Float?
  page_width   Float?
  units        String?
  isActive     Boolean   @default(true)
  created_at   DateTime  @default(now())
  created_by   String    @default("ADMIN")
  updated_at   DateTime? @updatedAt
  updated_by   String?

  @@map("page_size")
}

model TranscriptionType {
  transcription_type String               @id
  type_description   String?
  isActive           Boolean              @default(true)
  created_at         DateTime             @default(now())
  created_by         String               @default("ADMIN")
  updated_at         DateTime?            @updatedAt
  updated_by         String?
  AudioTranscription AudioTranscription[]

  @@map("transcription_type")
}

model AudioTranscription_LLMResponse {
  id                  String              @id @default(uuid()) @db.Uuid
  transcription_id    String?             @db.Uuid
  response_id         String?             @db.Uuid
  isActive            Boolean             @default(true)
  created_at          DateTime            @default(now())
  created_by          String              @default("ADMIN")
  updated_at          DateTime?           @updatedAt
  updated_by          String?
  responseLLM         ResponseLLM?        @relation(fields: [response_id], references: [responseId])
  audio_transcription AudioTranscription? @relation(fields: [transcription_id], references: [id])

  @@map("audio_transcription_llm_response")
}

model letterhead_section {
  letterhead_section_id     String                      @id @default(uuid()) @db.Uuid
  label                     String
  type                      String
  size_constraint           Int?
  isActive                  Boolean                     @default(true)
  created_at                DateTime                    @default(now())
  created_by                String                      @default("ADMIN")
  updated_at                DateTime?                   @updatedAt
  updated_by                String?
  doctor_letterhead_section doctor_letterhead_section[]

  @@map("letterhead_section")
}

model doctor_letterhead_section {
  id                    String             @id @default(uuid()) @db.Uuid
  letterhead_id         String             @db.Uuid
  letterhead_section_id String             @db.Uuid
  value                 String?
  isActive              Boolean            @default(true)
  created_at            DateTime           @default(now())
  created_by            String             @default("ADMIN")
  updated_at            DateTime?          @updatedAt
  updated_by            String?
  doctorletterhead      DoctorLetterHead   @relation(fields: [letterhead_id], references: [letterhead_id])
  letterhead_section    letterhead_section @relation(fields: [letterhead_section_id], references: [letterhead_section_id])
}

model Tenant {
  Id               String             @id @default(uuid()) @db.Uuid
  name             String?            @unique
  phone_number     String?
  email            String?
  isActive         Boolean            @default(true)
  created_at       DateTime           @default(now())
  created_by       String             @default("ADMIN")
  updated_at       DateTime?          @updatedAt
  updated_by       String?
  Patient          Patient[]
  TenantDoctor     TenantDoctor[]
  tbl_user_details tbl_user_details[]
  tbl_role_lk      tbl_role_lk[]

  @@map("tenant")
}

model TenantDoctor {
  id         String    @id @default(uuid()) @db.Uuid
  tenantId   String?   @db.Uuid
  doctorId   String?   @db.Uuid
  isActive   Boolean   @default(true)
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?
  doctor     Doctor?   @relation(fields: [doctorId], references: [doctor_id])
  tenant     Tenant?   @relation(fields: [tenantId], references: [Id])

  @@map("tenant_doctor")
}

model EncounterFeedback {
  id               String             @id @default(uuid()) @db.Uuid
  encounter_id     String?            @db.Uuid
  feedback         String?
  isActive         Boolean            @default(true)
  created_at       DateTime           @default(now())
  created_by       String             @default("ADMIN")
  updated_at       DateTime?          @updatedAt
  updated_by       String?
  encounter        Encounter?         @relation(fields: [encounter_id], references: [encounter_id])
  FeedbackEvidence FeedbackEvidence[]

  @@map("encounter_feedback")
}

model FeedbackEvidence {
  id                String             @id @default(uuid()) @db.Uuid
  feedbackId        String?            @db.Uuid
  url               String?
  isActive          Boolean            @default(true)
  created_at        DateTime           @default(now())
  created_by        String             @default("ADMIN")
  updated_at        DateTime?          @updatedAt
  updated_by        String?
  screenshot_url    String?
  encounterfeedback EncounterFeedback? @relation(fields: [feedbackId], references: [id])

  @@map("feedback_evidence")
}

model tbl_auto_code {
  id                  Int       @id @default(autoincrement())
  prefix              String?
  suffix              String?
  last_generated_code String?
  created_at          DateTime? @default(now()) @db.Timestamptz(3)
  created_by          String?
  updated_at          DateTime? @db.Timestamptz(3)
  updated_by          String?
  isActive            Boolean?  @default(true)
  last_increment      Int?
  digits              Int?

  @@map("tbl_auto_code")
}

model tbl_universal_picklist {
  Id                          String                        @id @default(uuid()) @db.Uuid
  desc_1                      String?
  desc_2                      String?
  isActive                    Boolean?                      @default(true)
  master_code                 String                        @unique @default(dbgenerated("generate_auto_code('UM'::text, ''::text, ''::text, ''::text)"))
  master_description          String?
  master_name                 String
  master_value_prefix         String
  created_by                  String?
  updated_by                  String?
  parent_code                 String?
  created_at                  DateTime?                     @default(now()) @db.Timestamptz(3)
  updated_at                  DateTime?                     @db.Timestamptz(3)
  tbl_universal_picklist_data tbl_universal_picklist_data[]

  @@map("tbl_universal_picklist")
}

model tbl_universal_picklist_data {
  Id                     String                 @id @default(uuid()) @db.Uuid
  desc_1                 String?
  desc_2                 String?
  isActive               Boolean?               @default(true)
  master_code            String
  master_code_prefix     String
  parent_code            String?
  record_description     String?
  record_id              String?
  record_name            String
  created_by             String?
  updated_by             String?
  parent_record_id       String?
  additional_info        Json?
  created_at             DateTime?              @default(now()) @db.Timestamptz(3)
  updated_at             DateTime?              @db.Timestamptz(3)
  picklist_id            String                 @db.Uuid
  tbl_universal_picklist tbl_universal_picklist @relation(fields: [picklist_id], references: [Id])

  @@map("tbl_universal_picklist_data")
}

model tbl_prompts {
  prompt_id         String    @id @default(uuid()) @db.Uuid
  prompt_user       String
  prompt_system     String?
  name              String?
  description       String?
  api_name          String?
  version           String    @default("1.0")
  temperature       Float?    @default(0.7)
  max_tokens        Int?
  top_p             Float?
  frequency_penalty Float?
  presence_penalty  Float?
  model_name        String
  prompt_variables  String?
  default_values    Json?
  additional_info   Json?
  category          String?
  tags              String?
  usage_count       Int?      @default(0)
  last_used         DateTime?
  isActive          Boolean   @default(true)
  is_template       Boolean?  @default(false)
  created_at        DateTime  @default(now()) @db.Timestamptz(3)
  created_by        String    @default("ADMIN")
  updated_at        DateTime? @updatedAt @db.Timestamptz(3)
  updated_by        String?

  @@map("tbl_prompts")
}

enum Gender {
  MALE
  FEMALE
  OTHER
  UNKNOWN
  RATHER_NOT_SAY
}

model tbl_permission_lk {
  permission_id   String  @id @default(uuid()) @db.Uuid
  key             String  @unique
  description     String? @db.Text
  type            String? @default("SYSTEM_DEFINED")
  permission_type String?

  tenant_id String? @db.Uuid

  isActive   Boolean   @default(true)
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt @db.Timestamptz(3)
  updated_by String?

  tbl_user_permission_mapping tbl_user_permission_mapping[]
  tbl_role_permission_mapping tbl_role_permission_mapping[]

  @@map("tbl_permission_lk")
}

model tbl_role_lk {
  role_id     String  @id @default(uuid()) @db.Uuid
  role_name   String?
  role_code   String?
  description String? @db.Text
  type        String? @default("SYSTEM_DEFINED")
  tenant_id   String? @db.Uuid

  isActive   Boolean   @default(true)
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt @db.Timestamptz(3)
  updated_by String?

  user_roles                  tbl_user_role_mapping[]
  tbl_role_permission_mapping tbl_role_permission_mapping[]
  tenant                      Tenant?                       @relation(fields: [tenant_id], references: [Id])

  @@index([role_name])
  @@map("tbl_role_lk")
}

model tbl_user_details {
  user_id         String    @id @default(uuid()) @db.Uuid
  user_name       String?
  first_name      String?
  last_name       String?
  email           String
  phone_number    String?
  password        String?
  login_id        String?
  profile_picture String?
  last_login      DateTime?
  tenant_id       String?   @db.Uuid

  isActive   Boolean   @default(true)
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt @db.Timestamptz(3)
  updated_by String?

  tbl_user_role_mapping       tbl_user_role_mapping[]
  tbl_user_permission_mapping tbl_user_permission_mapping[]
  tenant                      Tenant?                       @relation(fields: [tenant_id], references: [Id])

  @@map("tbl_user_details")
}

model tbl_user_role_mapping {
  user_role_map_id String  @id @default(uuid()) @db.Uuid
  user_id          String  @db.Uuid
  role_lk_id       String  @db.Uuid
  tenant_id        String? @db.Uuid

  isActive   Boolean   @default(true)
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt @db.Timestamptz(3)
  updated_by String?

  roleLookup       tbl_role_lk      @relation(fields: [role_lk_id], references: [role_id])
  tbl_user_details tbl_user_details @relation(fields: [user_id], references: [user_id])

  @@index([user_id])
  @@index([role_lk_id])
  @@map("tbl_user_role_mapping")
}

enum PermissionType {
  USER_DEFINED
  SYSTEM_DEFINED
}

model tbl_user_permission_mapping {
  user_permission_id String  @id @default(uuid()) @db.Uuid
  user_id            String  @db.Uuid
  permission_id      String  @db.Uuid
  type               String? @default("SYSTEM_DEFINED")

  isActive   Boolean   @default(true)
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt @db.Timestamptz(3)
  updated_by String?

  tbl_user_details  tbl_user_details  @relation(fields: [user_id], references: [user_id])
  tbl_permission_lk tbl_permission_lk @relation(fields: [permission_id], references: [permission_id])

  @@index([user_id])
  @@index([permission_id])
  @@map("tbl_user_permission_mapping")
}

model tbl_role_permission_mapping {
  role_permission_id String  @id @default(uuid()) @db.Uuid
  role_id            String  @db.Uuid
  permission_id      String  @db.Uuid
  type               String? @default("SYSTEM_DEFINED")

  isActive   Boolean   @default(true)
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt @db.Timestamptz(3)
  updated_by String?

  tbl_permission_lk tbl_permission_lk? @relation(fields: [permission_id], references: [permission_id])
  tbl_role_lk       tbl_role_lk?       @relation(fields: [role_id], references: [role_id])

  @@unique([role_id, permission_id], name: "role_permission_unique")
  @@map("tbl_role_permission_mapping")
}

model tbl_vital_types {
  vital_type_id      String  @id @default(uuid()) @db.Uuid
  unit_name          String?
  max_value          String?
  min_value          String?
  measurement_system String?
  vital_type_name    String
  data_type          String?
  display_name       String?
  notes              String? @db.Text
  reasoning          String? @db.Text
  show_case          String?

  isActive               Boolean                  @default(true)
  created_at             DateTime                 @default(now()) @db.Timestamptz(3)
  created_by             String                   @default("ADMIN")
  updated_at             DateTime?                @updatedAt @db.Timestamptz(3)
  updated_by             String?
  tbl_vital_measurements tbl_vital_measurements[]

  @@map("tbl_vital_types")
}

model tbl_vital_measurements {
  vital_measurement_id String    @id @default(uuid()) @db.Uuid
  vital_type_id        String    @db.Uuid
  encounter_id         String    @db.Uuid
  vital_value          String?
  measurement_time     DateTime?

  isActive   Boolean   @default(true)
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt @db.Timestamptz(3)
  updated_by String?

  tbl_vital_types tbl_vital_types @relation(fields: [vital_type_id], references: [vital_type_id])
  encounter       Encounter       @relation(fields: [encounter_id], references: [encounter_id])

  @@map("tbl_vital_measurements")
}

model tbl_org_setting {
  org_setting_id String  @id @default(uuid()) @db.Uuid
  key            String  @unique
  value          String
  config_type    String?
  data_type      String?
  description    String?

  isActive   Boolean   @default(true)
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt @db.Timestamptz(3)
  updated_by String?

  @@map("tbl_org_setting")
}
