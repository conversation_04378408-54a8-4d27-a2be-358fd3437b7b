import { Injectable, Logger } from '@nestjs/common';
import { tbl_permission_lk, tbl_role_lk } from '@prisma/client';
import { RolesRepository } from './roles.repository';
import { CreateRoleDto } from './dto/create-role.dto';

@Injectable()
export class RolesService {
  private readonly logger = new Logger(RolesService.name);
  constructor(
    private readonly rolesRepository: RolesRepository,
  ) { }

  async createRoles(data: CreateRoleDto, userEmail: string): Promise<tbl_role_lk[]> {
    return await this.rolesRepository.createRoles(data, userEmail);
  }

  async findAll() {
    return await this.rolesRepository.getRoles();
  }
}
