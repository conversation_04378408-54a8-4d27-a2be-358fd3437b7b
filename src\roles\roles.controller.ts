import { Controller, Get, Post, Body } from '@nestjs/common';
import { ApiResponseDTO } from 'src/common/dtos';
import { tbl_role_lk } from '@prisma/client';
import { CreateRoleDto } from './dto/create-role.dto';
import { RolesService } from './roles.service';

@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) { }

  @Post('create-role')
  async createRoles(@Body() data: CreateRoleDto, userEmail: string): Promise<ApiResponseDTO<tbl_role_lk[]>> {
    return { data: await this.rolesService.createRoles(data, userEmail) }
  }

  @Get()
  async findAllRoles() {
    return await this.rolesService.findAll();
  }
}
