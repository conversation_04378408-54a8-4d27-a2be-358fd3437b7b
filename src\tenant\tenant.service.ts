import { Injectable } from '@nestjs/common';
import { TenantRepository } from './tenant.repository';
import { CreateTenantDto } from './dto/tenant.dto';

@Injectable()
export class TenantService {
  constructor(private readonly tenantrepository: TenantRepository) {}
  async getAllTenants() {
    return await this.tenantrepository.getAllTenants();
  }
  async createTenant(model: CreateTenantDto, userEmail: string) {
    return await this.tenantrepository.createTenant(model, userEmail);
  }

  async getDoctorTenants(userEmail: string) {
    return await this.tenantrepository.getDoctorTenants(userEmail);
  }
}
