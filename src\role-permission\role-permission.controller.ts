import {
  Body,
  Controller,
  Get,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  Post,
  Put,
  Delete,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { RolePermissionService } from './role-permission.service';
import { CreateRolePermissionMappingDto } from './dtos/create-role-permission-mapping.dto';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { tbl_role_permission_mapping } from '@prisma/client';
import { PaginationQueryDTO, PaginatedResultDTO } from 'src/common/dtos';
import { PERMISSIONS } from 'src/common/decorators/permissions.decorator';

@ApiTags('RolePermission')
@ApiBearerAuth('access-token')
@Controller('role-permission')
export class RolePermissionController {
  constructor(private readonly service: RolePermissionService) { }

  @Post()
  @ApiOperation({ summary: 'Create role-permission mapping' })
  @PERMISSIONS('assign.permissions.basic', 'assign.permissions.all')
  async create(
    @Body() dto: CreateRolePermissionMappingDto,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.service.createMapping(dto, userEmail);
  }

  @Get()
  @ApiOperation({ summary: 'List role-permission mappings' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async findAll(
    @Query() pagination: PaginationQueryDTO,
    @Query('role_id') role_id?: string,
    @Query('permission_id') permission_id?: string,
  ): Promise<PaginatedResultDTO<tbl_role_permission_mapping>> {
    return await this.service.findMappings(
      role_id,
      permission_id,
      pagination.page,
      pagination.limit,
    );
  }

  @Get('roles-permissions')
  async rolesPermission() {
    return await this.service.getRolePermissions()
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get mapping by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return await this.service.findOne(id);
  }

  @Put('/:id')
  @ApiOperation({ summary: 'Update role-permission mapping' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() dto: Partial<CreateRolePermissionMappingDto>,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.service.updateMapping(id, dto, userEmail);
  }

  @Delete('/:id')
  @ApiOperation({ summary: 'Soft delete mapping' })
  async delete(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.service.deleteMapping(id, userEmail);
  }
}
