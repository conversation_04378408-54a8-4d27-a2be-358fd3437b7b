-- CreateTable
CREATE TABLE "tbl_role_lk" (
    "role_id" SERIAL NOT NULL,
    "role_name" VARCHAR(100) NOT NULL,
    "role_code" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_role_lk_pkey" PRIMARY KEY ("role_id")
);

--CreateIndex
CREATE UNIQUE INDEX "tbl_role_lk_role_name_key" ON "tbl_role_lk"("role_name");

--CreateIndex
CREATE UNIQUE INDEX "tbl_role_lk_role_code_key" ON "tbl_role_lk"("role_code");

--CreateIndex
CREATE INDEX "tbl_role_lk_isActive_idx" ON "tbl_role_lk"("isActive");

--CreateIndex
CREATE INDEX "tbl_role_lk_role_code_idx" ON "tbl_role_lk"("role_code");

--CreateIndex
CREATE INDEX "tbl_role_lk_created_at_idx" ON "tbl_role_lk"("created_at");
