import { Injectable, Logger, HttpStatus } from '@nestjs/common';
import { tbl_user_permission_mapping } from '@prisma/client';
import { UserPermissionRepository } from './user-permission.repository';
import { CreateUserPermissionMappingDto } from './dtos/create-user-permission-mapping.dto';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class UserPermissionService {
  private readonly logger = new Logger(UserPermissionService.name);

  constructor(
    private readonly repo: UserPermissionRepository,
    private readonly utilsService: UtilsService,
  ) {}

  async create(
    dto: CreateUserPermissionMappingDto,
    userEmail: string,
  ): Promise<tbl_user_permission_mapping> {
    try {
      return await this.repo.create(dto, userEmail);
    } catch (error) {
      this.logger.error('Error in service create:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create permission mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: string): Promise<tbl_user_permission_mapping> {
    try {
      return await this.repo.findOne(id);
    } catch (error) {
      this.logger.error('Error in service findOne:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Permission mapping not found',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async findAll(): Promise<tbl_user_permission_mapping[]> {
    try {
      return await this.repo.findAll();
    } catch (error) {
      this.logger.error('Error in service findAll:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch mappings',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async update(
    id: string,
    dto: Partial<CreateUserPermissionMappingDto>,
    userEmail: string,
  ): Promise<tbl_user_permission_mapping> {
    try {
      return await this.repo.update(id, dto, userEmail);
    } catch (error) {
      this.logger.error('Error in service update:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update permission mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async remove(
    id: string,
    userEmail: string,
  ): Promise<tbl_user_permission_mapping> {
    try {
      return await this.repo.remove(id, userEmail);
    } catch (error) {
      this.logger.error('Error in service remove:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete permission mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
