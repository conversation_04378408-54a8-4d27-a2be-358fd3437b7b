import {
  <PERSON>,
  Controller,
  Get,
  Param,
  ParseU<PERSON><PERSON><PERSON><PERSON>,
  Post,
  Put,
  Delete,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserrolemappingService } from './userrolemapping.service';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { tbl_user_role_mapping } from '@prisma/client';
import { CreateUserRoleMappingDto } from './dtos/dtos/create-user-role-mapping.dto';

@ApiTags('UserRoleMapping')
@ApiBearerAuth('access-token')
@Controller('userrolemapping')
export class UserrolemappingController {
  constructor(private readonly userRoleService: UserrolemappingService) {}

  @Post()
  @ApiOperation({ summary: 'Create a user-role mapping' })
  async create(
    @Body() dto: CreateUserRoleMappingDto,
    @GetUserEmail() userEmail: string,
  ): Promise<tbl_user_role_mapping> {
    return await this.userRoleService.create(dto, userEmail);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user-role mapping by ID' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<tbl_user_role_mapping> {
    return await this.userRoleService.findOne(id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all user-role mappings' })
  async findAll(): Promise<tbl_user_role_mapping[]> {
    return await this.userRoleService.findAll();
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a user-role mapping' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() dto: Partial<CreateUserRoleMappingDto>,
    @GetUserEmail() userEmail: string,
  ): Promise<tbl_user_role_mapping> {
    return await this.userRoleService.update(id, dto, userEmail);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a user-role mapping (soft delete)' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUserEmail() userEmail: string,
  ): Promise<tbl_user_role_mapping> {
    return await this.userRoleService.remove(id, userEmail);
  }
}
