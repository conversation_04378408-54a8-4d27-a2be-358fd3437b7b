-- CreateTable
CREATE TABLE "letterhead_section" (
    "letterhead_section_id" UUID NOT NULL,
    "label" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "size_constraint" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "letterhead_section_pkey" PRIMARY KEY ("letterhead_section_id")
);

INSERT INTO letterhead_section 
(letterhead_section_id, label, type)
VALUES
(gen_random_uuid(), 'Doctor''s Name', 'string'),
(gen_random_uuid(), 'Doctor''s Credentials', 'string'),
(gen_random_uuid(), 'Doctor Rgn No.', 'string'),
(gen_random_uuid(), 'Clinic / Hospital Name', 'string'),
(gen_random_uuid(), 'Clinic / Hospital Logo', 'image'),
(gen_random_uuid(), 'Address', 'string'),
(gen_random_uuid(), 'Phone Number', 'string'),
(gen_random_uuid(), 'Email', 'string'),
(gen_random_uuid(), 'Website', 'string');