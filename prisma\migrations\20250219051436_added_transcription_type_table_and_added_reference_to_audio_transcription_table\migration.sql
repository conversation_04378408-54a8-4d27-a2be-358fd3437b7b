-- AlterTable
ALTER TABLE "audio_transcription" ADD COLUMN     "transcription_type" TEXT;

-- CreateTable
CREATE TABLE "transcription_type" (
    "transcription_type" TEXT NOT NULL,
    "type_description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "transcription_type_pkey" PRIMARY KEY ("transcription_type")
);

INSERT INTO transcription_type 
(transcription_type, type_description) 
VALUES 
('ORIGINAL', 'Audio Transcription captured originally during the patient visit'),
('APPENDED', 'Audio Transcription appended after the visit has concluded to provide additional information to the doctor notes');

-- AddForeignKey
ALTER TABLE "audio_transcription" ADD CONSTRAINT "audio_transcription_transcription_type_fkey" FOREIGN KEY ("transcription_type") REFERENCES "transcription_type"("transcription_type") ON DELETE SET NULL ON UPDATE CASCADE;
