import {
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  HttpStatus,
} from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { UpsertDoctorDto } from './dtos/DoctorDetails.dto';
import { Doctor, Specialties } from '@prisma/client';
import { create } from 'domain';
import { UtilsService } from '../common/utils/utils.service';
import { HttpStatusCode } from 'axios';

@Injectable()
export class DoctorRepository {
  private readonly logger = new Logger(DoctorRepository.name);
  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  /**
   * Finds a doctor by email. If not found, creates a new doctor record.
   * @param email Doctor's email from JWT token
   * @param firstName Optional first name from JWT or user input
   * @param lastName Optional last name from JWT or user input
   * @returns The found or newly created doctor record
   */
  async getOrCreateDoctorByEmail(
    email: string,
    firstName: string = null,
    lastName: string = null,
  ) {
    try {
      let doctor = await this.prisma.doctor.findUnique({
        where: { email },
      });

      if (!doctor) {
        doctor = await this.prisma.doctor.create({
          data: {
            email,
            first_name: firstName,
            last_name: lastName,
          },
        });
      }

      return doctor;
    } catch (error) {
      this.logger.error(
        `Error in getOrCreateDoctorByEmail:  email:${email} error:${error?.stack}`,
      );
      throw this.utilsService.formatErrorResponse(
        error,
        `Failed to retrieve or create doctor profile email:${email}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async upsertDoctor(
    model: UpsertDoctorDto,
    userEmail: string,
  ): Promise<Doctor> {
    try {
      let doctorExists = await this.prisma.doctor.findFirst({
        where: {
          email: model.email,
          isActive: true,
        },
        include: {
          TenantDoctor: {
            where: { isActive: true },
            select: { id: true, tenantId: true },
            orderBy: { created_at: 'asc' },
          },
        },
      });

      let isVerified: boolean = false;
      //check isverify flag
      if (
        !model.email ||
        !model.first_name ||
        !model.last_name ||
        !model.country_of_practice ||
        !model.specialty ||
        !model.phone ||
        model.email.trim() == '' ||
        model.first_name.trim() == '' ||
        model.last_name.trim() == '' ||
        model.country_of_practice.trim() == '' ||
        model.specialty.trim() == '' ||
        model.phone.trim() == ''
      ) {
        isVerified = false;
      } else {
        isVerified = true;
      }

      let doctor;
      if (!doctorExists) {
        doctor = await this.prisma.doctor.create({
          data: {
            email: model.email,
            first_name: model.first_name,
            last_name: model.last_name ?? null,
            phone: model.phone ?? null,
            created_by: userEmail,
            specialty: model.specialty ? model.specialty : null,
            country_of_practice: model.country_of_practice
              ? model.country_of_practice
              : null,
            ehr: model.ehr ? model.ehr : null,
            isVerified: isVerified,
            TenantDoctor: model.tenant_id
              ? {
                  create: {
                    tenantId: model.tenant_id,
                    created_by: userEmail,
                  },
                }
              : undefined,
          },
          include: {
            TenantDoctor: {
              where: { isActive: true },
            },
          },
        });
      } else {
        let updateOp;
        if (
          doctorExists.TenantDoctor[0] &&
          doctorExists.TenantDoctor.length > 0 &&
          doctorExists.TenantDoctor[0]?.id
        ) {
          updateOp = {
            update: {
              where: { id: doctorExists.TenantDoctor[0]?.id },
              data: {
                tenantId: model.tenant_id,
                updated_by: userEmail,
              },
            },
          };
        } else {
          updateOp = {
            create: {
              tenantId: model.tenant_id,
              created_by: userEmail,
            },
          };
        }

        doctor = await this.prisma.doctor.update({
          where: {
            email: doctorExists.email,
            doctor_id: doctorExists.doctor_id,
            isActive: true,
          },
          data: {
            first_name: model.first_name,
            last_name: model.last_name ?? null,
            phone: model.phone ?? null,
            updated_by: userEmail,
            specialty: model.specialty ? model.specialty : null,
            country_of_practice: model.country_of_practice
              ? model.country_of_practice
              : null,
            ehr: model.ehr ? model.ehr : null,
            isVerified: isVerified,
            TenantDoctor: model.tenant_id ? updateOp : undefined,
          },
          include: {
            TenantDoctor: {
              where: { isActive: true },
            },
          },
        });
      }

      return doctor;
    } catch (error) {
      this.logger.error(
        `error in upsert doctor : email:${model.email} error:${error?.stack}`,
      );
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update doctor information',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getDoctorByEmail(email: string): Promise<Doctor> {
    try {
      const doctor = await this.prisma.doctor.findFirst({
        where: {
          email,
          isActive: true,
        },
        include: {
          TenantDoctor: {
            where: {
              isActive: true,
            },
            select: {
              tenant: {
                where: {
                  isActive: true,
                },
                select: {
                  name: true,
                  Id: true,
                  isActive: true,
                },
              },
            },
          },
        },
      });

      // Check if doctor exists
      if (!doctor) {
        throw new NotFoundException(`doctor not found for email:${email}`);
      }

      // Filter TenantDoctor to only include entries with active tenants
      doctor.TenantDoctor = doctor.TenantDoctor.filter(
        (td) => td?.tenant && td?.tenant?.isActive === true,
      );

      return doctor;
    } catch (error) {
      this.logger.error(
        `error in fetching the doctor by email: email:${email} stack:${error?.stack} `,
      );
      throw error;
    }
  }

  async getAllSpecialities(): Promise<Specialties[]> {
    try {
      const specialities = await this.prisma.specialties.findMany({
        where: {
          isActive: true,
        },
        orderBy: {
          order: 'asc',
        },
      });
      if (!specialities || specialities.length == 0) {
        throw new HttpException(
          'specialities not found',
          HttpStatusCode.NoContent,
        );
      }
      return specialities;
    } catch (error) {
      this.logger.error(`error fetching specialities: error:${error?.stack}`);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch specialities',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
