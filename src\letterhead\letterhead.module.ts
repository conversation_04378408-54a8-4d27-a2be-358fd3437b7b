import { Modu<PERSON> } from '@nestjs/common';
import { LetterheadService } from './letterhead.service';
import { LetterheadController } from './letterhead.controller';
import { LetterHeadRepository } from './letterhead.repository';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';
import { ConfigService } from '@nestjs/config';

@Module({
  controllers: [LetterheadController],
  providers: [LetterheadService,LetterHeadRepository,ConfigService,s3FileUploadService],
})
export class LetterheadModule {}
