import { Controller, Get, Query } from '@nestjs/common';
import { PaginatedResultDTO, PaginationQueryDTO } from 'src/common/dtos';
import { PlanService } from './plan.service';
import { Plan } from '@prisma/client';

@Controller('plans')
export class PlanController {
  constructor(private readonly planservice: PlanService) {}

  @Get()
  async getPlans(
    @Query() pagination: PaginationQueryDTO,
  ): Promise<PaginatedResultDTO<Plan>> {
    const { page, limit } = pagination;
    return await this.planservice.getPlans(page, limit);
  }
}
