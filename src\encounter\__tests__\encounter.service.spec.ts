import { Test, TestingModule } from '@nestjs/testing';
import { EncounterService } from '../encounter.service';
import { EncounterRepository } from '../encounter.repository';
import { CreateEncounterDTO } from '../dtos/CreateEncounter.dto';

describe('EncounterService', () => {
  let service: EncounterService;
  let repository: EncounterRepository;

  const mockEncounterRepository = {
    getAllEncounters: jest.fn(),
    getEncounterById: jest.fn(),
    insertEncounter: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EncounterService,
        {
          provide: EncounterRepository,
          useValue: mockEncounterRepository,
        },
      ],
    }).compile();

    service = module.get<EncounterService>(EncounterService);
    repository = module.get<EncounterRepository>(EncounterRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAllEncounters', () => {
    it('should call repository.getAllEncounters and return its result', async () => {
      const fakeResult = [{ encounterId: 'abc123', patient: {} }];
      repository.getAllEncounters.mockResolvedValue(fakeResult);

      const result = await service.getAllEncounters(1, 10);
      expect(repository.getAllEncounters).toHaveBeenCalledWith(1, 10);
      expect(result).toEqual(fakeResult);
    });
  });

  describe('getEncounterById', () => {
    it('should call repository.getEncounterById and return its result', async () => {
      const encounterId = 'uuid-123';
      const fakeEncounter = { encounterId: encounterId, patient: {} };
      repository.getEncounterById.mockResolvedValue(fakeEncounter);

      const result = await service.getEncounterById(encounterId);
      expect(repository.getEncounterById).toHaveBeenCalledWith(encounterId);
      expect(result).toEqual(fakeEncounter);
    });
  });

  describe('insertEncounter', () => {
    it('should call repository.insertEncounter and return its result', async () => {
      const dto: CreateEncounterDTO = {
        patientFirstName: 'John',
        patientLastName: 'Doe',
        patientDOB: '1980-01-01',
        patientGender: 'MALE',
        patientPhone: '',
        phoneCountryCode: '',
      };
      const fakeInsertResult = { encounterId: 'enc123', patientId: 'pat456' };
      repository.insertEncounter.mockResolvedValue(fakeInsertResult);

      const result = await service.insertEncounter(dto);
      expect(repository.insertEncounter).toHaveBeenCalledWith(dto);
      expect(result).toEqual(fakeInsertResult);
    });
  });
});
