import {
  Body,
  Controller,
  Get,
  Param,
  ParseU<PERSON><PERSON>ipe,
  Put,
  Query,
  Post,
  Delete,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { UserdetailsService } from './userdetails.service';
import { CreateUserDetailsDto } from './dtos/CreateUserDetails.dto';
import { PaginatedResultDTO, PaginationQueryDTO } from 'src/common/dtos';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { tbl_user_details } from '@prisma/client';
import { PERMISSIONS } from 'src/common/decorators/permissions.decorator';

@ApiTags('UserDetails')
@ApiBearerAuth('access-token')
@Controller('userdetails')
export class UserdetailsController {
  constructor(private readonly userdetailsService: UserdetailsService) {}

  @Post()
  @ApiOperation({ summary: 'Create user details' })
  @PERMISSIONS('user.create.self', 'user.create.org')
  async createUser(
    @Body() model: CreateUserDetailsDto,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.userdetailsService.createUser(model, userEmail);
  }

  @Get()
  @ApiOperation({ summary: 'Get users with email & uesrname' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'email',
    required: false,
    type: String,
    description: 'Search user by email',
  })
  @ApiQuery({
    name: 'user_name',
    required: false,
    type: String,
    description: 'Search user by username',
  })
  @PERMISSIONS('user.read.self', 'user.read.org')
  async findUsers(
    @Query() pagination: PaginationQueryDTO,
    @Query('email') email: string,
    @Query('user_name') user_name: string,
    @GetUserEmail() userEmail: string,
  ) {
    const { page, limit } = pagination;
    return await this.userdetailsService.findUsers(
      email,
      user_name,
      userEmail,
      page,
      limit,
    );
  }

  @Get('/:userId')
  @ApiOperation({ summary: 'Get user details by ID' })
  @PERMISSIONS('user.read.self', 'user.read.org')
  async findOne(
    @Param('userId', ParseUUIDPipe) userId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.userdetailsService.findOne(userId, userEmail);
  }

  @Put('/:userId')
  @ApiOperation({ summary: 'Update user details' })
  @PERMISSIONS('user.update.self', 'user.update.org')
  async updateUser(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() model: Partial<CreateUserDetailsDto>,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.userdetailsService.updateUser(userId, model, userEmail);
  }

  @Delete('/:userId')
  @ApiOperation({ summary: 'Delete user details' })
  @PERMISSIONS('user.delete.self', 'user.delete.org')
  async deleteUser(
    @Param('userId', ParseUUIDPipe) userId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.userdetailsService.deleteUser(userId, userEmail);
  }
}
