/*
  Warnings:

  - The primary key for the `_AudioTranscriptionToPatient` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - A unique constraint covering the columns `[A,B]` on the table `_AudioTranscriptionToPatient` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "_AudioTranscriptionToPatient" DROP CONSTRAINT "_AudioTranscriptionToPatient_AB_pkey";

-- CreateTable
CREATE TABLE "tbl_auto_code" (
    "id" SERIAL NOT NULL,
    "prefix" TEXT,
    "suffix" TEXT,
    "last_generated_code" TEXT,
    "created_at" TIMESTAMPTZ(3) DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT,
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,
    "isActive" BOOLEAN DEFAULT true,
    "last_increment" INTEGER,
    "digits" INTEGER,

    CONSTRAINT "tbl_auto_code_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "_AudioTranscriptionToPatient_AB_unique" ON "_AudioTranscriptionToPatient"("A", "B");
