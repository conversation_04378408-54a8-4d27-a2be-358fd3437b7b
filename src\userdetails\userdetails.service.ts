import { Injectable } from '@nestjs/common';
import { UserdetailsRepository } from './userdetails.repository';
import { CreateUserDetailsDto } from './dtos/CreateUserDetails.dto';
import { tbl_user_details } from '@prisma/client';
import {
  PaginatedResultDTO,
  
} from 'src/common/dtos';
@Injectable()
export class UserdetailsService {
  constructor(private readonly userRepo: UserdetailsRepository) {}

  async createUser(model: CreateUserDetailsDto, userEmail: string): Promise<tbl_user_details> {
    return await this.userRepo.createUser(model, userEmail);
  }

  async findUsers(
    email: string,
    user_name: string,
    userEmail: string,
    page = 1,
    limit = 10,
  ) : Promise<PaginatedResultDTO<tbl_user_details>> {
    return await this.userRepo.findUsers(email, user_name, userEmail, page, limit);
  }

  async findOne(userId: string, userEmail: string): Promise<tbl_user_details> {
    return await this.userRepo.getUserById(userId);
  }

  async updateUser(
    userId: string,
    model: Partial<CreateUserDetailsDto>,
    userEmail: string,
  ): Promise<tbl_user_details> {
    return await this.userRepo.updateUser(userId, model, userEmail);
  }

  async deleteUser(userId: string, userEmail: string): Promise<tbl_user_details> {
    return await this.userRepo.deleteUser(userId, userEmail);
  }
}
