import { <PERSON>du<PERSON> } from '@nestjs/common';
import { VitalController } from './vitals.controller';
import { TranscribeModule } from 'src/transcribe/transcribe.module';
import { VitalRepository } from './vitals.repository';
import { VitalService } from './vitals.service';
import { TranscribeService } from 'aws-sdk';
import { TranscriptionsRepository } from 'src/transcriptions/transcriptions.repository';

@Module({
  imports: [TranscribeModule],
  controllers: [VitalController],
  providers: [
    VitalRepository,
    VitalService,
    TranscriptionsRepository,
    TranscribeService,
  ],
})
export class VitalModule {}
