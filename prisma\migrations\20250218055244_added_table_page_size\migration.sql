-- CreateTable
CREATE TABLE "page_size" (
    "page_size_id" UUID NOT NULL,
    "page_size" TEXT NOT NULL,
    "page_height" DOUBLE PRECISION,
    "page_width" DOUBLE PRECISION,
    "units" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "page_size_pkey" PRIMARY KEY ("page_size_id")
);

-- CreateIndex
CREATE INDEX "idx_doctor_letterhead_doctor" ON "doctor_letterhead"("doctor_id");
