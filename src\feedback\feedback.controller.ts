import {
  Body,
  Controller,
  Param,
  ParseUUI<PERSON>ipe,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FeedbackService } from './feedback.service';
import { CreateFeedbackDto, FeedbackEvidenceDto } from './dto/feedback.dto';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('feedback')
export class FeedbackController {
  constructor(private readonly feedbackService: FeedbackService) {}

  //POST /feedback/encounter/:encounterId
  @Post('encounter/:encounterId')
  async createFeedback(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @Body() data: CreateFeedbackDto,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.feedbackService.createFeedback(
      userEmail,
      encounterId,
      data,
    );
  }
  // POST /feedback/:feedbackId/evidence
  @Post(':feedbackId/evidence')
  async createFeedbackEvidence(
    @Param('feedbackId', ParseUUIDPipe) feedbackId: string,
    @Body() model: FeedbackEvidenceDto,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.feedbackService.createFeedbackEvidence(
      userEmail,
      feedbackId,
      model,
    );
  }

  // POST /feedback/:feedbackId/evidence/screenshot
  @UseInterceptors(FileInterceptor('screenshot'))
  @Post(':feedbackId/evidence/screenshot')
  async uploadFeedbackScreenshot(
    @Param('feedbackId', ParseUUIDPipe) feedbackId: string,
    @UploadedFile() file: Express.Multer.File,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.feedbackService.uploadFeedbackScreenshot(file, feedbackId);
  }
}
