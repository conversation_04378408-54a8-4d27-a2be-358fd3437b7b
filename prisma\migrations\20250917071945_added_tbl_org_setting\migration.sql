/*
  Warnings:

  - A unique constraint covering the columns `[role_id,permission_id]` on the table `tbl_role_permission_mapping` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateTable
CREATE TABLE "tbl_org_setting" (
    "org_setting_id" UUID NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "config_type" TEXT,
    "data_type" TEXT,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_org_setting_pkey" PRIMARY KEY ("org_setting_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tbl_org_setting_key_key" ON "tbl_org_setting"("key");

-- CreateIndex
-- CREATE UNIQUE INDEX "tbl_role_permission_mapping_role_id_permission_id_key" ON "tbl_role_permission_mapping"("role_id", "permission_id");
