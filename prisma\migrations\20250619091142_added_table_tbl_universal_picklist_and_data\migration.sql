-- AlterTable
ALTER TABLE "_AudioTranscriptionToPatient" ADD CONSTRAINT "_AudioTranscriptionToPatient_AB_pkey" PRIMARY KEY ("A", "B");

-- DropIndex
DROP INDEX "_AudioTranscriptionToPatient_AB_unique";

-- CreateTable
CREATE TABLE "tbl_universal_picklist" (
    "Id" UUID NOT NULL,
    "desc_1" TEXT,
    "desc_2" TEXT,
    "isActive" BOOLEAN DEFAULT true,
    "master_code" TEXT NOT NULL DEFAULT generate_auto_code('UM'::text, ''::text, ''::text, ''::text),
    "master_description" TEXT,
    "master_name" TEXT NOT NULL,
    "master_value_prefix" TEXT NOT NULL,
    "created_by" TEXT,
    "updated_by" TEXT,
    "parent_code" TEXT,
    "created_at" TIMESTAMPTZ(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3),

    CONSTRAINT "tbl_universal_picklist_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "tbl_universal_picklist_data" (
    "Id" UUID NOT NULL,
    "desc_1" TEXT,
    "desc_2" TEXT,
    "isActive" BOOLEAN DEFAULT true,
    "master_code" TEXT NOT NULL,
    "master_code_prefix" TEXT NOT NULL,
    "parent_code" TEXT,
    "record_description" TEXT,
    "record_id" TEXT,
    "record_name" TEXT NOT NULL,
    "created_by" TEXT,
    "updated_by" TEXT,
    "parent_record_id" TEXT,
    "additional_info" JSONB,
    "created_at" TIMESTAMPTZ(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3),
    "picklist_id" UUID NOT NULL,

    CONSTRAINT "tbl_universal_picklist_data_pkey" PRIMARY KEY ("Id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tbl_universal_picklist_master_code_key" ON "tbl_universal_picklist"("master_code");

-- AddForeignKey
ALTER TABLE "tbl_universal_picklist_data" ADD CONSTRAINT "tbl_universal_picklist_data_picklist_id_fkey" FOREIGN KEY ("picklist_id") REFERENCES "tbl_universal_picklist"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;
