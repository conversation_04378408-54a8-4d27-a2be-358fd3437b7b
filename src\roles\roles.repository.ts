import { HttpStatus, Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "src/common/prisma/prisma.service";
import { UtilsService } from "src/common/utils/utils.service";
import { tbl_permission_lk, tbl_role_lk } from "@prisma/client";
import { CreateRoleDto } from "./dto/create-role.dto";

@Injectable()
export class RolesRepository {
    private readonly logger = new Logger(RolesRepository.name);
    constructor(private readonly prismaservice: PrismaService,
        private readonly utilsService: UtilsService
    ) { }


    //Create roles
    async createRoles(data: CreateRoleDto, userEmail: string): Promise<tbl_role_lk[]> {
        try {
            const roles = await this.prismaservice.tbl_role_lk.createManyAndReturn({
                data: data.info.map((info) => ({
                    role_name: info.role_name,
                    role_code: info.role_code,
                    description: info.description,
                    type: info.type ?? 'SYSTEM_DEFINED', // fallback if not passed
                    tenant_id: info.tenant_id,
                    isActive: info.isActive ?? true,
                    created_by: userEmail || info.created_by || 'ADMIN',
                })),
            });

            return roles;
        }
        catch (error) {
            this.logger.error('Error creating roles:', error?.stack);
            throw this.utilsService.formatErrorResponse(
                error,
                'Failed to create roles',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    //Get roles
    async getRoles(): Promise<tbl_role_lk[]> {
        try {
            const roles = await this.prismaservice.tbl_role_lk.findMany();
            return roles;
        } catch (error) {
            this.logger.error('Error fetching roles:', error?.stack);
            throw this.utilsService.formatErrorResponse(
                error,
                'Failed to fetch roles',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

}