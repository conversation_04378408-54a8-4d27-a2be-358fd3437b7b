-- CreateTable
CREATE TABLE "subscription" (
    "subscriptionId" UUID NOT NULL,
    "planId" UUID,
    "doctor_id" UUID,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "status" TEXT DEFAULT 'ACTIVE',
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endDate" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "subscription_pkey" PRIMARY KEY ("subscriptionId")
);

-- AddForeignKey
ALTER TABLE "subscription" ADD CONSTRAINT "subscription_planId_fkey" FOREIGN KEY ("planId") REFERENCES "plan"("planId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddF<PERSON><PERSON><PERSON>ey
ALTER TABLE "subscription" ADD CONSTRAINT "subscription_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "doctor"("doctor_id") ON DELETE SET NULL ON UPDATE CASCADE;
