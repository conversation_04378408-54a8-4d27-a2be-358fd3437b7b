import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateNotesDTO {
  @ApiProperty({
    description: 'Identifier for the type of note',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsNotEmpty()
  noteTypeId: string;
  extraNoteTypes?: CreateNoteSectionDto[];
}

export class CreateNoteTypeDto {
  note_type_name: string;
  sections: CreateNoteSectionDto[]; // Array of sections to link
}

export class CreateNoteSectionDto {
  section_name: string;
  section_id: string;
}

