-- AlterTable
ALTER TABLE "labs" ADD COLUMN     "status" TEXT,
ADD COLUMN     "status_reason" TEXT;

-- CreateTable
CREATE TABLE "status" (
    "status" TEXT NOT NULL,
    "label" VARCHAR(50) NOT NULL,
    "description" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "status_pkey" PRIMARY KEY ("status")
);

-- AddForeignKey
ALTER TABLE "labs" ADD CONSTRAINT "labs_status_fkey" FOREIGN KEY ("status") REFERENCES "status"("status") ON DELETE SET NULL ON UPDATE CASCADE;
