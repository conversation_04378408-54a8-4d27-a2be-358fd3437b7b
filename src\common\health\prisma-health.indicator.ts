import { Injectable } from '@nestjs/common';
import {
  HealthIndicator,
  HealthIndicatorResult,
  HealthCheckError,
} from '@nestjs/terminus';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class PrismaHealthIndicator extends HealthIndicator {
  constructor(private prisma: PrismaService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      // Perform a simple query to check DB connectivity
      await this.prisma.$queryRaw`SELECT 1`;
      const result = this.getStatus(key, true);
      return result;
    } catch (error) {
      const result = this.getStatus(key, false);
      throw new HealthCheckError('Database check failed', result);
    }
  }
}
