import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import {
  Logger,
  UseGuards,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { LiveTranscribeService } from './live-transcribe.service';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import * as jwt from 'jsonwebtoken';
import * as jwksRsa from 'jwks-rsa';

/**
 * WebSocket gateway for handling real-time transcription
 * Handles connections, room management, and transcription events
 */
@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class LiveTranscribeGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  private readonly logger = new Logger(LiveTranscribeGateway.name);
  private jwksClient: jwksRsa.JwksClient;

  @WebSocketServer()
  server: Server;

  constructor(private readonly liveTranscribeService: LiveTranscribeService) {
    this.jwksClient = jwksRsa({
      jwksUri: `${process.env.AUTH0_ISSUER_URL}.well-known/jwks.json`,
      cache: true,
      rateLimit: true,
    });
  }

  private async getSigningKey(kid: string): Promise<string> {
    try {
      const key = await this.jwksClient.getSigningKey(kid);

      return key.getPublicKey();
    } catch (error) {
      this.logger.error(`Error getting signing key: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  private async validateToken(token: string): Promise<any> {
    try {
      // First decode the token without verification to get the header
      const decoded: any = jwt.decode(token, { complete: true });

      if (!decoded || !decoded.header || !decoded.header.kid) {
        throw new UnauthorizedException('Invalid token format');
      }

      // Get the signing key using the key ID from the token header
      const publicKey = await this.getSigningKey(decoded.header.kid);

      // Verify the token with the correct key
      const payload = jwt.verify(token, publicKey, {
        algorithms: ['RS256'],
        issuer: process.env.AUTH0_ISSUER_URL,
        audience: process.env.AUTH0_AUDIENCE,
      });

      return payload;
    } catch (error) {
      this.logger.error(`Token validation error: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * Handle client connections and assign them to encounter-specific rooms
   */
  async handleConnection(client: Socket) {
    try {
      // Extract token from handshake auth or query
      const token = client.handshake.headers.token;

      if (!token) {
        this.logger.warn(
          `Client ${client.id} attempted to connect without token`,
        );
        client.disconnect(true);
        return;
      }

      // Validate the token
      const payload = await this.validateToken(token as string);
      if (!payload || !payload.email) {
        this.logger.warn(`Client ${client.id} provided invalid token`);
        client.disconnect(true);
        return;
      }

      // Store user info in socket
      client.data.user = payload;

      this.logger.log(`Client authenticated and connected: ${client.id}`);

      // Join a room based on the query parameter
      const encounterId = client.handshake.query.encounterId as string;

      if (encounterId) {
        const roomName = `encounter-${encounterId}`;
        client.join(roomName);
        this.logger.log(`Client ${client.id} joined room: ${roomName}`);
      } else {
        this.logger.warn(`Client ${client.id} connected without encounterId`);
      }
    } catch (error) {
      this.logger.error(`Connection error: ${error.message}`);
      client.disconnect(true);
    }
  }

  /**
   * Handle client disconnections
   */
  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  /**
   * Start a transcription session for an encounter
   * Sets up event listeners and notifies clients
   */
  @SubscribeMessage('start_transcription')
  handleStartTranscription(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { encounterId: string },
  ) {
    const roomName = `encounter-${data.encounterId}`;

    if (!data.encounterId) {
      client.to(roomName).emit('error', { message: 'encounterId is required' });
      return { success: false, error: 'encounterId is required' };
    }

    this.logger.log(
      `Starting transcription session for encounter: ${data.encounterId}`,
    );

    // Start a real-time transcription session with AWS Transcribe
    this.liveTranscribeService
      .startRealtimeTranscription(data.encounterId)
      .then((encounterId) => {
        // Store the session ID in the client data
        client.data.realtimeSessionId = encounterId;

        // Set up transcription event listener
        this.setupTranscriptionListener(encounterId, roomName);

        // Set up error event listener
        this.setupErrorListener(encounterId, roomName);

        // Notify all clients that transcription has started
        this.server.to(roomName).emit('transcription_started', {
          encounterId: data.encounterId,
          timestamp: new Date().toISOString(),
        });
      })
      .catch((error) => {
        this.logger.error(`Failed to start transcription: ${error.message}`);
        client.to(roomName).emit('error', {
          message: 'Failed to start transcription',
          error: error.message,
        });
      });
  }

  /**
   * Set up listener for transcription events
   */
  private setupTranscriptionListener(
    encounterId: string,
    roomName: string,
  ): void {
    this.liveTranscribeService.onRealtimeTranscription(
      encounterId,
      'transcription',
      (transcription) => {
        // Emit the transcription to all clients in the encounter room
        this.server.to(roomName).emit('transcription_chunk', {
          transcription,
          timestamp: new Date().toISOString(),
          isFinal: true, // AWS transcription handles this internally
        });
      },
    );
  }

  /**
   * Set up listener for error events
   */
  private setupErrorListener(encounterId: string, roomName: string): void {
    this.liveTranscribeService.onRealtimeTranscription(
      encounterId,
      'error',
      (error) => {
        this.logger.error(`Transcription error: ${error}`);
        this.server.to(roomName).emit('error', {
          message: 'Transcription error',
          error,
        });
      },
    );
  }

  /**
   * Process audio chunks from clients and send to transcription service
   */
  @SubscribeMessage('audio_chunk')
  async handleAudioChunk(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { encounterId: string; audioChunk: Buffer },
  ) {
    const roomName = `encounter-${data.encounterId}`;
    if (!data.encounterId) {
      client.to(roomName).emit('error', {
        message: 'encounterId is required',
      });
      return { success: false, error: 'encounterId is required' };
    }

    try {
      // this.logger.debug(
      //   `Received audio chunk for encounter: ${data.encounterId}`,
      // );
      this.server.to(roomName).emit('audio_chunk', {
        message: 'Audio chunk received',
        audioChunk: data.audioChunk,
      });
      // Send audio chunk to the transcription service
      if (data && data.audioChunk) {
        await this.liveTranscribeService.sendAudioChunk(
          data.encounterId,
          data.audioChunk,
        );
      }
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error processing audio chunk: ${error.message}`,
        error.stack,
      );

      this.server.to(roomName).emit('error', {
        message: 'Failed to process audio chunk',
        error: error.message,
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * End a transcription session and emit final results
   */
  @SubscribeMessage('end_transcription')
  async handleEndTranscription(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: {
      encounterId: string;
      rawTranscription: string;
      userEmail: string;
      duration?: string;
      s3Uri?: string;
      fileSize?: number;
      mimeType?: string;
      originalFilename?: string;
      type: string;
      patient_id: string;
    },
  ) {
    if (!data.encounterId) {
      return { success: false, error: 'encounterId is required' };
    }

    const roomName = `encounter-${data.encounterId}`;
    this.logger.log(`Ending transcription for encounter: ${data.encounterId}`);

    try {
      // Process the final transcription if provided
      let final_refined_transcription: any = '';
      if (data.rawTranscription) {
        final_refined_transcription =
          await this.liveTranscribeService.processFinalTranscription(
            data.originalFilename
              ? data.originalFilename
              : `LIVE_TRANSCRITION_${data.encounterId}`,
            data.encounterId,
            data.rawTranscription,
            data.userEmail,
            data.s3Uri ? data.s3Uri : '',
            data.fileSize || 0,
            data.mimeType || 'Live Transcription',
            data.duration ? data.duration : '0',
            data.type,
            data.patient_id,
          );
      }
      // End the transcription session and get the final transcription
      const transcription =
        await this.liveTranscribeService.endRealtimeTranscription(
          data.encounterId,
        );

      // Emit the final transcription to all clients in the encounter room
      this.server.to(roomName).emit('transcription_complete', {
        final_transcription: data.rawTranscription,
        transcription_data: final_refined_transcription,
        timestamp: new Date().toISOString(),
      });

      // Leave the room
      client.leave(roomName);

      return {
        success: true,
        transcription,
        final_refined_transcription,
      };
    } catch (error) {
      this.logger.error(
        `Error finalizing transcription: ${error.message}`,
        error.stack,
      );

      this.server.to(roomName).emit('error', {
        message: 'Failed to finalize transcription',
        error: error.message,
      });

      return { success: false, error: error.message };
    }
  }
}
